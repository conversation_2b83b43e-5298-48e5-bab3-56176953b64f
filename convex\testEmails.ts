import { action } from "./_generated/server";
import { v } from "convex/values";
import { internal } from "./_generated/api";

// Test function to send welcome email
export const testWelcomeEmail = action({
  args: {
    email: v.string(),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    try {
      await ctx.runAction(internal.email.sendVerificationEmail, {
        email: args.email,
        verificationUrl:
          "https://ai.erzen.tk/verify?token=test-verification-token",
      });

      console.log(`Test welcome email sent to ${args.email}`);
      return null;
    } catch (error) {
      console.error("Failed to send test welcome email:", error);
      throw error;
    }
  },
});

// Test function to send subscription email
export const testSubscriptionEmail = action({
  args: {
    email: v.string(),
    planName: v.optional(
      v.union(v.literal("pro"), v.literal("ultra"), v.literal("max"))
    ),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    try {
      await ctx.runAction(internal.email.sendSubscriptionEmail, {
        email: args.email,
        userName: "Test User",
        planName: args.planName || "pro",
        planPrice: "$19.99",
        currentPeriodEnd: "February 15, 2025",
        dashboardUrl: "https://ai.erzen.tk/settings",
      });

      console.log(
        `Test subscription email sent to ${args.email} for plan ${args.planName || "pro"}`
      );
      return null;
    } catch (error) {
      console.error("Failed to send test subscription email:", error);
      throw error;
    }
  },
});
