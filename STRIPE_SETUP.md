# Stripe Integration Setup Guide

## Overview

Your AI application now supports Stripe subscriptions for the four usage plans:

- **Free**: 100 credits, 10 searches, $1.0 max spending
- **Pro**: 500 credits, 100 searches, $8.0 max spending
- **Ultra**: 2500 credits, 1000 searches, $20.0 max spending
- **Max**: 20000 credits, 5000 searches, $120.0 max spending

## Files Modified/Created

### New Files:

- `convex/stripe.ts` - Stripe configuration and subscription management
- `convex/webhooks.ts` - Webhook handler for Stripe events
- `STRIPE_SETUP.md` - This setup guide

### Modified Files:

- `package.json` - Added Stripe dependency
- `convex/schema.ts` - Added subscription tables and fields
- `convex/usage.ts` - Integrated with Stripe subscription status
- `convex/http.ts` - Added webhook endpoint

## Environment Variables Required

Add these environment variables to your Convex environment:

```bash
# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_... # Your Stripe secret key
STRIPE_WEBHOOK_SECRET=whsec_... # Webhook endpoint secret
STRIPE_PRO_PRICE_ID=price_... # Price ID for Pro plan
STRIPE_ULTRA_PRICE_ID=price_... # Price ID for Ultra plan
STRIPE_MAX_PRICE_ID=price_... # Price ID for Max plan
```

## Stripe Dashboard Setup

### 1. Create Products and Prices

In your Stripe Dashboard, create three products with recurring prices:

**Pro Plan:**

- Name: "Pro Plan"
- Price: $8.00/month (or your preferred amount)
- Copy the Price ID to `STRIPE_PRO_PRICE_ID`

**Ultra Plan:**

- Name: "Ultra Plan"
- Price: $20.00/month (or your preferred amount)
- Copy the Price ID to `STRIPE_ULTRA_PRICE_ID`

**Max Plan:**

- Name: "Max Plan"
- Price: $120.00/month (or your preferred amount)
- Copy the Price ID to `STRIPE_MAX_PRICE_ID`

### 2. Configure Webhook Endpoint

1. Go to **Developers → Webhooks** in Stripe Dashboard
2. Click **Add endpoint**
3. Set endpoint URL to: `https://your-convex-deployment.convex.site/webhooks/stripe`
4. Select these events:
   - `checkout.session.completed`
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`
5. Copy the **Signing secret** to `STRIPE_WEBHOOK_SECRET`

## Frontend Integration

### Create Checkout Session

```typescript
import { api } from "../convex/_generated/api";
import { useAction } from "convex/react";

function SubscriptionButton({ plan }: { plan: "pro" | "ultra" | "max" }) {
  const createCheckout = useAction(api.stripe.createCheckoutSession);

  const handleSubscribe = async () => {
    const { url } = await createCheckout({
      plan,
      successUrl: `${window.location.origin}/subscription/success`,
      cancelUrl: `${window.location.origin}/subscription/cancel`,
    });

    window.location.href = url;
  };

  return (
    <button onClick={handleSubscribe}>
      Subscribe to {plan} Plan
    </button>
  );
}
```

### Check Subscription Status

```typescript
import { api } from "../convex/_generated/api";
import { useQuery } from "convex/react";

function SubscriptionStatus() {
  const subscription = useQuery(api.subscriptions.getSubscription);

  if (!subscription?.hasSubscription) {
    return <div>No active subscription</div>;
  }

  return (
    <div>
      <p>Plan: {subscription.plan}</p>
      <p>Status: {subscription.status}</p>
      <p>Next billing: {new Date(subscription.currentPeriodEnd).toLocaleDateString()}</p>
      {subscription.cancelAtPeriodEnd && (
        <p>⚠️ Subscription will cancel at period end</p>
      )}
    </div>
  );
}
```

### Customer Portal Access

```typescript
import { api } from "../convex/_generated/api";
import { useAction } from "convex/react";

function CustomerPortalButton() {
  const createPortalSession = useAction(api.stripe.createPortalSession);

  const handlePortal = async () => {
    const { url } = await createPortalSession({
      returnUrl: window.location.href,
    });

    window.location.href = url;
  };

  return (
    <button onClick={handlePortal}>
      Manage Billing
    </button>
  );
}
```

### Cancel Subscription

```typescript
import { api } from "../convex/_generated/api";
import { useAction } from "convex/react";

function CancelSubscriptionButton() {
  const cancelSubscription = useAction(api.stripe.cancelSubscription);

  const handleCancel = async (immediate = false) => {
    await cancelSubscription({ immediate });
    // Show success message
  };

  return (
    <div>
      <button onClick={() => handleCancel(false)}>
        Cancel at Period End
      </button>
      <button onClick={() => handleCancel(true)}>
        Cancel Immediately
      </button>
    </div>
  );
}
```

## API Reference

### Available Actions

- `api.stripe.createCheckoutSession` - Create subscription checkout
- `api.stripe.cancelSubscription` - Cancel subscription
- `api.stripe.createPortalSession` - Access customer portal

### Available Queries

- `api.subscriptions.getSubscription` - Get current subscription status
- `api.usage.get` - Get usage limits and subscription info

### Webhook Events Handled

- **checkout.session.completed** - Subscription creation
- **customer.subscription.updated** - Subscription changes
- **customer.subscription.deleted** - Subscription cancellation
- **invoice.payment_succeeded** - Successful payment
- **invoice.payment_failed** - Failed payment

## Usage Integration

The usage system automatically:

1. **Checks subscription status** when calculating limits
2. **Resets usage** at subscription billing periods
3. **Downgrades to free plan** when subscriptions expire
4. **Enforces plan limits** based on subscription status

## Testing

### Test Mode Setup

1. Use Stripe test keys (`sk_test_...` and `pk_test_...`)
2. Use test webhooks endpoint
3. Use Stripe test cards:
   - Success: `4242 4242 4242 4242`
   - Decline: `4000 0000 0000 0002`

### Webhook Testing

Use Stripe CLI to test webhooks locally:

```bash
stripe listen --forward-to https://your-convex-deployment.convex.site/webhooks/stripe
```

## Security Considerations

1. **Environment Variables**: Keep all Stripe keys secure
2. **Webhook Verification**: Webhooks are verified using Stripe signatures
3. **User Authentication**: All subscription actions require authenticated users
4. **Idempotency**: Webhook events are tracked to prevent duplicate processing

## Troubleshooting

### Common Issues

1. **Webhook failures**: Check webhook secret and endpoint URL
2. **Subscription not updating**: Verify webhook events are being received
3. **Price ID errors**: Ensure price IDs match your Stripe dashboard
4. **Environment variables**: Verify all required env vars are set

### Monitoring

- Check Convex logs for webhook processing
- Monitor Stripe Dashboard for failed payments
- Track subscription metrics in Stripe Dashboard

## Support

For issues with this integration:

1. Check Convex deployment logs
2. Review Stripe Dashboard for webhook delivery status
3. Verify environment variables are correctly set
4. Test with Stripe CLI in development mode
