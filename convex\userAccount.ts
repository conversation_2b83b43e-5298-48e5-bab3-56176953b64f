import { mutation, internalMutation } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";
import { v } from "convex/values";
import { internal } from "./_generated/api";
import { action } from "./_generated/server";
import { api } from "./_generated/api";
import { internalQuery } from "./_generated/server";

export const updateProfile = mutation({
  args: {
    name: v.string(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    await ctx.db.patch(userId, {
      name: args.name,
    });
  },
});

export const changePassword = mutation({
  args: {
    currentPassword: v.string(),
    newPassword: v.string(),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // TODO: Change password - depends on auth library implementation
    throw new Error("Password change not implemented yet");
  },
});

export const deleteAllConversations = mutation({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Call the internal function with the userId
    await ctx.runMutation(internal.userAccount.internalDeleteAllConversations, {
      userId,
    });
  },
});

// Internal version that can be called with a specific userId
export const internalDeleteAllConversations = internalMutation({
  args: {
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    // Get all user conversations
    const conversations = await ctx.db
      .query("conversations")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .collect();

    // Collect all storage IDs that need to be deleted
    const storageIdsToDelete: Array<string> = [];

    // Delete all messages for each conversation and collect storage IDs
    for (const conversation of conversations) {
      const messages = await ctx.db
        .query("messages")
        .withIndex("by_conversation", (q) =>
          q.eq("conversationId", conversation._id)
        )
        .collect();

      for (const message of messages) {
        // Collect storage IDs from attachments
        if (message.attachments) {
          for (const attachment of message.attachments) {
            if (attachment.storageId) {
              storageIdsToDelete.push(attachment.storageId);
            }
          }
        }

        await ctx.db.delete(message._id);
      }

      // Delete conversation branches
      const branches = await ctx.db
        .query("conversationBranches")
        .withIndex("by_conversation", (q) =>
          q.eq("conversationId", conversation._id)
        )
        .collect();

      for (const branch of branches) {
        await ctx.db.delete(branch._id);
      }

      // Delete the conversation
      await ctx.db.delete(conversation._id);
    }

    // Delete all storage files
    for (const storageId of storageIdsToDelete) {
      try {
        await ctx.storage.delete(storageId as any);
      } catch (error) {
        console.warn(`Failed to delete storage file ${storageId}:`, error);
      }
    }

    // Delete all file metadata records associated with these storage IDs
    for (const storageId of storageIdsToDelete) {
      try {
        const fileRecord = await ctx.db
          .query("uploadedFiles")
          .withIndex("by_storage_id", (q) =>
            q.eq("storageId", storageId as any)
          )
          .unique();

        if (fileRecord) {
          await ctx.db.delete(fileRecord._id);
        }
      } catch (error) {
        console.warn(`Failed to delete file metadata for ${storageId}:`, error);
      }
    }
  },
});

/**
 * Completely delete a user account and all associated data
 * Handles GDPR compliance by removing all user data across all tables
 * Cancels any active subscriptions
 */
export const deleteAccount = action({
  args: {},
  returns: v.null(),
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Step 1: Cancel any active subscriptions
    try {
      const usage = await ctx.runQuery(
        internal.userAccount.getUserUsageForDeletion,
        { userId }
      );

      if (usage?.stripeSubscriptionId) {
        // Cancel the subscription through Stripe
        await ctx.runAction(api.stripe.cancelSubscription, {
          immediate: true,
        });
      }
    } catch (error) {
      console.error("Error canceling subscription:", error);
      // Continue with account deletion even if subscription cancellation fails
    }

    // Step 2: Delete all user data via internal mutation
    await ctx.runMutation(internal.userAccount.deleteAllUserData, { userId });

    return null;
  },
});

export const getUserUsageForDeletion = internalQuery({
  args: { userId: v.id("users") },
  returns: v.union(
    v.null(),
    v.object({ stripeSubscriptionId: v.optional(v.string()) })
  ),
  handler: async (ctx, args) => {
    const usage = await ctx.db
      .query("userUsage")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .unique();
    if (!usage) {
      return null;
    }
    return {
      stripeSubscriptionId: usage.stripeSubscriptionId,
    };
  },
});

export const deleteAllUserData = internalMutation({
  args: { userId: v.id("users") },
  returns: v.null(),
  handler: async (ctx, args) => {
    // Delete conversations and related data
    await ctx.runMutation(internal.userAccount.internalDeleteAllConversations, {
      userId: args.userId,
    });

    // Delete all user memories
    const memories = await ctx.db
      .query("userMemories")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .collect();

    for (const memory of memories) {
      await ctx.db.delete(memory._id);
    }

    // Delete user API keys
    const apiKeys = await ctx.db
      .query("apiKeys")
      .withIndex("by_user_provider", (q) => q.eq("userId", args.userId))
      .collect();

    for (const apiKey of apiKeys) {
      await ctx.db.delete(apiKey._id);
    }

    // Delete custom providers
    const customProviders = await ctx.db
      .query("customProviders")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .collect();

    for (const provider of customProviders) {
      await ctx.db.delete(provider._id);
    }

    // Delete MCP servers
    const mcpServers = await ctx.db
      .query("mcpServers")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .collect();

    for (const server of mcpServers) {
      await ctx.db.delete(server._id);
    }

    // Delete n8n servers
    const n8nServers = await ctx.db
      .query("n8nServers")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .collect();

    for (const server of n8nServers) {
      await ctx.db.delete(server._id);
    }

    // Delete user usage records
    const usageRecords = await ctx.db
      .query("userUsage")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .collect();

    for (const record of usageRecords) {
      await ctx.db.delete(record._id);
    }

    // Delete GitHub accounts
    const githubAccounts = await ctx.db
      .query("githubAccounts")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .collect();

    for (const account of githubAccounts) {
      await ctx.db.delete(account._id);
    }

    // Delete auth accounts (OAuth connections)
    const authAccounts = await ctx.db
      .query("authAccounts")
      .withIndex("userIdAndProvider", (q) => q.eq("userId", args.userId))
      .collect();

    for (const account of authAccounts) {
      await ctx.db.delete(account._id);
    }

    // Delete user instructions
    const instructions = await ctx.db
      .query("userInstructions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .collect();

    for (const instruction of instructions) {
      await ctx.db.delete(instruction._id);
    }

    // Finally delete the user account itself
    await ctx.db.delete(args.userId);

    return null;
  },
});
