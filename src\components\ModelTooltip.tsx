import React from "react";
import { ModelInfo, formatTokenCount, PROVIDER_CONFIGS } from "@/lib/models";
import { <PERSON>, Wrench, X, Check, Brain } from "lucide-react";
import { HoverCard, HoverCardTrigger, HoverCardContent } from "@/components/ui/hover-card";
import { getProviderIcon } from "@/lib/model-ui";

interface ModelTooltipProps {
  modelInfo: ModelInfo;
  children: React.ReactNode;
}

export function ModelTooltip({ modelInfo, children }: ModelTooltipProps) {
  // Get primary provider (first in modelProviders array or fallback to legacy provider)
  const primaryProvider = modelInfo.modelProviders?.[0] || {
    provider: modelInfo.provider,
    modelId: modelInfo.id,
    pricing: modelInfo.pricing,
    priority: 1
  };
  const provider = PROVIDER_CONFIGS[primaryProvider.provider];
  
  // Get company/group name, fallback to provider name for backward compatibility
  const companyName = modelInfo.groupName || provider?.name || modelInfo.provider;

  return (
    <HoverCard>
      <HoverCardTrigger asChild>
        {children}
      </HoverCardTrigger>
      <HoverCardContent className="w-96 p-4" side="right" align="start">
        {/* Header */}
        <div className="flex items-start gap-3 mb-4">
          <div className="w-10 h-10 rounded-lg bg-muted/50 flex items-center justify-center text-lg flex-shrink-0">
            {(() => {
              const IconComponent = getProviderIcon(primaryProvider.provider);
              return <IconComponent size={20} />;
            })()}
          </div>
          <div className="min-w-0 flex-1">
            <div className="text-label-xl text-foreground mb-1">{modelInfo.displayName}</div>
            <div className="text-sm text-muted-foreground">{companyName}</div>
          </div>
          {(modelInfo.pricing || primaryProvider.pricing) && (() => {
            const pricing = modelInfo.pricing || primaryProvider.pricing!;
            const inputTokens = 10000;
            const outputTokens = 3000;
            const inputCost = (pricing.input / 1_000_000) * inputTokens;
            const outputCost = (pricing.output / 1_000_000) * outputTokens;
            const credits = Math.ceil((inputCost + outputCost) * 100);
            
            return (
              <div className="px-2 py-1 bg-primary/10 text-primary rounded text-caption-lg">
                {credits}x
              </div>
            );
          })()}
        </div>

        {/* Description */}
        <div className="text-sm text-muted-foreground mb-4 leading-relaxed">
          {modelInfo.description}
        </div>

        {/* Token Limits */}
        <div className="grid grid-cols-3 gap-2 text-sm mb-4">
          <div className="bg-muted/50 p-2 rounded-lg">
                      <div className="text-label-md text-muted-foreground mb-1">Input</div>
          <div className="text-label-lg text-foreground">{formatTokenCount(modelInfo.maxInputTokens)}</div>
          </div>
          <div className="bg-muted/50 p-2 rounded-lg">
            <div className="text-label-md text-muted-foreground mb-1">Output</div>
            <div className="text-label-lg text-foreground">{formatTokenCount(modelInfo.maxOutputTokens)}</div>
          </div>
          <div className="bg-muted/50 p-2 rounded-lg">
            <div className="text-label-md text-muted-foreground mb-1">Context</div>
            <div className="text-label-lg text-foreground">{formatTokenCount(modelInfo.contextWindow)}</div>
          </div>
        </div>
      
        {/* Capabilities */}
        <div className="grid grid-cols-3 gap-2 text-sm mb-4">
          <div className={`p-2 rounded-lg border ${
            modelInfo.supportsTools
              ? 'bg-primary/10 border-primary/20 text-primary'
              : 'bg-muted/50 border-border text-muted-foreground'
          }`}>
            <div className="flex items-center justify-between mb-1">
              <Wrench size={14} />
              {modelInfo.supportsTools ? <Check size={12} className="text-primary" /> : <X size={12} />}
            </div>
            <div className="font-medium">Tools</div>
            <div className="text-xs opacity-80">
              {modelInfo.supportsTools ? 'Available' : 'Not available'}
            </div>
          </div>
          <div className={`p-2 rounded-lg border ${
            modelInfo.isMultimodal
              ? 'bg-primary/10 border-primary/20 text-primary'
              : 'bg-muted/50 border-border text-muted-foreground'
          }`}>
            <div className="flex items-center justify-between mb-1">
              <Eye size={14} />
              {modelInfo.isMultimodal ? <Check size={12} className="text-primary" /> : <X size={12} />}
            </div>
            <div className="font-medium">Vision</div>
            <div className="text-xs opacity-80">
              {modelInfo.isMultimodal ? 'Available' : 'Text only'}
            </div>
          </div>
          <div className={`p-2 rounded-lg border ${
            modelInfo.supportsThinking
              ? 'bg-primary/10 border-primary/20 text-primary'
              : 'bg-muted/50 border-border text-muted-foreground'
          }`}>
            <div className="flex items-center justify-between mb-1">
              <Brain size={14} />
              {modelInfo.supportsThinking ? <Check size={12} className="text-primary" /> : <X size={12} />}
            </div>
            <div className="font-medium">Thinking</div>
            <div className="text-xs opacity-80">
              {modelInfo.supportsThinking ? 'Available' : 'Not available'}
            </div>
          </div>
        </div>
      
        {/* Provider list removed for cleaner, modern look */}
      
        {/* Capability Tags */}
        <div className="flex flex-wrap gap-1">
          {modelInfo.capabilities.map((cap) => (
            <span
              key={cap}
              className="px-2 py-1 rounded bg-muted/50 text-muted-foreground text-xs font-medium"
            >
              {cap}
            </span>
          ))}
        </div>
      </HoverCardContent>
    </HoverCard>
  );
}
