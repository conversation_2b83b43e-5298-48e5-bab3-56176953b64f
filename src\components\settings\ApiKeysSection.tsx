import { useState } from "react";
import { useQ<PERSON>y, useMutation } from "convex/react";
import { toast } from "sonner";
import { api } from "../../../convex/_generated/api";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  SettingsCard,
  SettingsCardContent,
  SettingsCardDescription,
  SettingsCardHeader,
  SettingsCardTitle,
} from "@/components/settings/SettingsCard";
import { Input } from "@/components/ui/input";
import {
  Eye,
  EyeOff,
  Trash2,
  Zap,
  Infinity as InfinityIcon,
  Lock,
  Key,
  Shield,
  CheckCircle,
  Sparkles,
} from "lucide-react";
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from "@/components/ui/alert";
import { Doc } from "../../../convex/_generated/dataModel";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { PROVIDER_CONFIGS, getModelsByGroup, getModelProviders, MODEL_INFO } from "@/lib/models";

type ApiKeyProvider = Doc<"apiKeys">["provider"];

// API key specific configurations for providers that need special handling
const API_KEY_CONFIGS = {
  openai: { keyPlaceholder: "sk-...", description: "GPT models from OpenAI" },
  google: { keyPlaceholder: "AIza...", description: "Gemini models from Google" },
  anthropic: { keyPlaceholder: "sk-ant-...", description: "Claude models from Anthropic" },
  openrouter: { keyPlaceholder: "sk-or-...", description: "Access to multiple AI models" },
  groq: { keyPlaceholder: "gsk_...", description: "Ultra-fast inference" },
  deepseek: { keyPlaceholder: "sk-...", description: "Reasoning and coding models" },
  grok: { keyPlaceholder: "xai-...", description: "Elon's AI with real-time data" },
  cohere: { keyPlaceholder: "co_...", description: "Enterprise-grade language models" },
  mistral: { keyPlaceholder: "...", description: "European AI models" },
  cerebras: { keyPlaceholder: "csk-...", description: "Ultra-fast inference with Cerebras" },
  github: { keyPlaceholder: "ghp_... or github_pat_...", description: "GitHub Models inference API with PAT token" },
  tavily: { keyPlaceholder: "tvly-...", description: "Real-time web search API" },
  openweather: { keyPlaceholder: "...", description: "Weather data API" },
  firecrawl: { keyPlaceholder: "fc-...", description: "AI-ready web scraping and crawling" },
};

export function ApiKeysSection() {
  const apiKeyInfo = useQuery(api.apiKeys.getApiKeyInfo) || [];
  const upsertApiKey = useMutation(api.apiKeys.upsert);
  const removeApiKey = useMutation(api.apiKeys.remove);

  const [apiKeys, setApiKeys] = useState<Record<string, string>>({});
  const [showKeys, setShowKeys] = useState<Record<string, boolean>>({});

  // Get models grouped by company for provider impact analysis
  const modelsByGroup = getModelsByGroup();
  
  // Helper function to get models available for a specific provider
  const getModelsForProvider = (provider: string) => {
    const models: Array<{model: string, groupName: string, isPrimary: boolean, hasMultiProvider: boolean}> = [];
    
    Object.values(MODEL_INFO).forEach((modelInfo) => {
      if (modelInfo.hidden) return;
      
      const providers = getModelProviders(modelInfo.id);
      const providerEntry = providers.find(p => p.provider === provider);
      
      if (providerEntry) {
        models.push({
          model: modelInfo.displayName,
          groupName: modelInfo.groupName,
          isPrimary: providerEntry.priority === 1,
          hasMultiProvider: providers.length > 1
        });
      }
    });
    
    return models.sort((a, b) => {
      // Sort by primary first, then by group name, then by model name
      if (a.isPrimary !== b.isPrimary) return a.isPrimary ? -1 : 1;
      if (a.groupName !== b.groupName) return a.groupName.localeCompare(b.groupName);
      return a.model.localeCompare(b.model);
    });
  };

  // Helper function to get fallback information for a provider
  const getFallbackInfo = (provider: string) => {
    const fallbackModels: Array<{model: string, primaryProvider: string}> = [];
    
    Object.values(MODEL_INFO).forEach((modelInfo) => {
      if (modelInfo.hidden) return;
      
      const providers = getModelProviders(modelInfo.id);
      const hasThisProvider = providers.some(p => p.provider === provider);
      const primaryProvider = providers.find(p => p.priority === 1)?.provider;
      
      if (hasThisProvider && primaryProvider !== provider && providers.length > 1) {
        fallbackModels.push({
          model: modelInfo.displayName,
          primaryProvider: PROVIDER_CONFIGS[primaryProvider]?.name || primaryProvider
        });
      }
    });
    
    return fallbackModels;
  };

  const handleSave = async (provider: ApiKeyProvider, key: string) => {
    if (!key) {
      toast.error("API key cannot be empty.");
      return;
    }
    toast.promise(
      upsertApiKey({
        provider,
        apiKey: key,
      }),
      {
        loading: "Saving API key...",
        success: "API key saved!",
        error: "Failed to save API key.",
      },
    );
  };

  const handleRemove = async (provider: ApiKeyProvider) => {
    toast.promise(
      removeApiKey({ provider }),
      {
        loading: "Removing API key...",
        success: "API key removed!",
        error: "Failed to remove API key.",
      },
    );
  };

  return (
    <div className="space-y-6">
      {/* Compact Header */}
      <div className="space-y-4">
        <div className="flex items-center justify-between mb-4">
          <div className="w-2 h-2 bg-zinc-400/60 rounded-full" />
          <div className="text-xs text-zinc-600 font-mono">API Keys</div>
        </div>
        <div className="text-center space-y-3">
          <div className="flex justify-center mb-3">
            <div className="p-2 bg-gradient-to-br from-blue-900/60 to-blue-800/40 rounded-lg border border-blue-700/40">
              <Key className="h-6 w-6 text-blue-300" />
            </div>
          </div>
          <h2 className="text-2xl font-bold text-white mb-2">API Keys Management</h2>
          <p className="text-base text-zinc-400 max-w-2xl mx-auto leading-relaxed">
            Configure your own API keys for unlimited access to AI models
          </p>
        </div>
      </div>

      {/* Compact Information Cards */}
      <div className="grid md:grid-cols-2 gap-4">
        {/* Built-in vs Your Keys Card */}
        <div className="relative group h-full">
          <div className="absolute -inset-1 bg-gradient-to-r from-blue-600/0 via-blue-500/15 to-purple-600/0 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-sm" />
          <div className="relative h-full bg-gradient-to-br from-zinc-900/80 via-slate-900/60 to-zinc-900/80 border border-zinc-700/60 rounded-lg p-4 backdrop-blur-sm flex flex-col">
            {/* Compact Card Header */}
            <div className="flex items-start gap-3 mb-4">
              <div className="p-2 bg-gradient-to-br from-blue-900/80 to-purple-900/60 rounded-lg border border-blue-700/50 flex-shrink-0">
                <Sparkles className="h-4 w-4 text-blue-300" />
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-white mb-1">Built-in vs. Your Keys</h3>
                <p className="text-xs text-zinc-400 leading-relaxed">
                  Choose between managed service or your own keys
                </p>
              </div>
            </div>
            
            {/* Compact Content */}
            <div className="space-y-3 flex-1">
              {/* Built-in Keys */}
              <div className="relative bg-gradient-to-r from-zinc-800/50 to-slate-800/30 border border-zinc-700/40 rounded-md p-3 transition-all duration-300 hover:border-zinc-600/60">
                <div className="flex items-start gap-2">
                  <div className="p-1 bg-blue-900/60 rounded border border-blue-700/40 mt-0.5">
                    <Zap className="h-3 w-3 text-blue-300" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-medium text-blue-200 text-sm">Built-in Keys</span>
                      <Badge className="bg-blue-900/50 text-blue-300 border-blue-700/50 text-xs">Free</Badge>
                    </div>
                    <p className="text-xs text-zinc-400 leading-relaxed">
                      Free usage with monthly limits
                    </p>
                  </div>
                </div>
              </div>

              {/* Your Keys */}
              <div className="relative bg-gradient-to-r from-zinc-800/50 to-slate-800/30 border border-zinc-700/40 rounded-md p-3 transition-all duration-300 hover:border-zinc-600/60">
                <div className="flex items-start gap-2">
                  <div className="p-1 bg-green-900/60 rounded border border-green-700/40 mt-0.5">
                    <InfinityIcon className="h-3 w-3 text-green-300" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-medium text-green-200 text-sm">Your Keys</span>
                      <Badge className="bg-green-900/50 text-green-300 border-green-700/50 text-xs">Unlimited</Badge>
                    </div>
                    <p className="text-xs text-zinc-400 leading-relaxed">
                      Unlimited usage, no restrictions
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Compact Footer */}
            <div className="mt-3 pt-3 border-t border-zinc-700/50">
              <div className="flex items-center gap-2 text-xs text-zinc-500">
                <CheckCircle className="h-3 w-3" />
                <span>Switch anytime</span>
              </div>
            </div>
          </div>
        </div>

        {/* Security Card */}
        <div className="relative group h-full">
          <div className="absolute -inset-1 bg-gradient-to-r from-green-600/0 via-emerald-500/15 to-green-600/0 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-sm" />
          <div className="relative h-full bg-gradient-to-br from-zinc-900/80 via-slate-900/60 to-zinc-900/80 border border-zinc-700/60 rounded-lg p-4 backdrop-blur-sm flex flex-col">
            {/* Compact Card Header */}
            <div className="flex items-start gap-3 mb-4">
              <div className="p-2 bg-gradient-to-br from-green-900/80 to-emerald-900/60 rounded-lg border border-green-700/50 flex-shrink-0">
                <Shield className="h-4 w-4 text-green-300" />
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-white mb-1">🔒 Encryption & Security</h3>
                <p className="text-xs text-zinc-400 leading-relaxed">
                  Enterprise-grade security measures
                </p>
              </div>
            </div>
            
            {/* Compact Content */}
            <div className="space-y-3 flex-1">
              {/* Encryption */}
              <div className="relative bg-gradient-to-r from-zinc-800/50 to-slate-800/30 border border-zinc-700/40 rounded-md p-3 transition-all duration-300 hover:border-zinc-600/60">
                <div className="flex items-start gap-2">
                  <div className="p-1 bg-green-900/60 rounded border border-green-700/40 mt-0.5">
                    <Lock className="h-3 w-3 text-green-300" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-medium text-green-200 text-sm">End-to-End Encryption</span>
                      <Badge className="bg-green-900/50 text-green-300 border-green-700/50 text-xs">AES-256</Badge>
                    </div>
                    <p className="text-xs text-zinc-400 leading-relaxed">
                      Keys encrypted before storage
                    </p>
                  </div>
                </div>
              </div>

              {/* Industry Standard */}
              <div className="relative bg-gradient-to-r from-zinc-800/50 to-slate-800/30 border border-zinc-700/40 rounded-md p-3 transition-all duration-300 hover:border-zinc-600/60">
                <div className="flex items-start gap-2">
                  <div className="p-1 bg-emerald-900/60 rounded border border-emerald-700/40 mt-0.5">
                    <CheckCircle className="h-3 w-3 text-emerald-300" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-medium text-emerald-200 text-sm">Industry Standards</span>
                      <Badge className="bg-emerald-900/50 text-emerald-300 border-emerald-700/50 text-xs">SOC 2</Badge>
                    </div>
                    <p className="text-xs text-zinc-400 leading-relaxed">
                      GDPR compliant, regular audits
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Compact Footer */}
            <div className="mt-3 pt-3 border-t border-zinc-700/50">
              <div className="flex items-center gap-2 text-xs text-zinc-500">
                <Shield className="h-3 w-3" />
                <span>Zero-access architecture</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Compact API Keys Grid */}
      <div className="space-y-4">
        <div className="flex items-center gap-3">
          <div className="p-1.5 bg-gradient-to-br from-zinc-800/60 to-slate-800/40 rounded-md border border-zinc-700/40">
            <Key className="h-4 w-4 text-zinc-300" />
          </div>
          <h3 className="text-lg font-semibold text-zinc-200">Configure API Keys</h3>
          <div className="flex-1 h-px bg-gradient-to-r from-zinc-700/50 via-zinc-600/30 to-transparent" />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
          {Object.entries(PROVIDER_CONFIGS).map(([provider, config], index) => {
            const keyInfo = apiKeyInfo.find(
              (info) => info.provider === provider,
            );
            const hasUserKey = keyInfo?.hasUserKey || false;

            return (
              <div key={provider} className="relative group">
                {/* Compact hover glow effect */}
                <div className={`absolute -inset-1 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-sm ${
                  hasUserKey 
                    ? 'bg-gradient-to-r from-green-600/0 via-green-500/15 to-emerald-600/0' 
                    : 'bg-gradient-to-r from-zinc-600/0 via-zinc-500/10 to-zinc-600/0'
                }`} />
                
                {/* Compact main card */}
                <div className={`relative bg-gradient-to-br from-zinc-900/80 via-slate-900/40 to-zinc-900/80 backdrop-blur-md border rounded-lg transition-all duration-300 hover:border-zinc-700/80 shadow-lg group-hover:transform group-hover:scale-[1.01] ${
                  hasUserKey ? 'border-green-700/60' : 'border-zinc-800/60'
                }`}>
                  <div className="p-4">
                    {/* Compact Card Header */}
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-2">
                        <div className={`w-1.5 h-1.5 rounded-full transition-colors duration-300 ${
                          hasUserKey ? 'bg-green-400/80 shadow-sm shadow-green-400/30' : 'bg-zinc-600/60'
                        }`} />
                        <h4 className="font-medium text-zinc-200">{config.name}</h4>
                      </div>
                      <div className="flex items-center gap-1.5">
                        <Badge 
                          variant={hasUserKey ? "default" : "secondary"} 
                          className={`text-xs ${
                            hasUserKey 
                              ? 'bg-green-900/60 text-green-300 border-green-700/60' 
                              : 'bg-zinc-800/60 text-zinc-400 border-zinc-700/60'
                          }`}
                        >
                          {hasUserKey ? "✓ Custom" : "Built-in"}
                        </Badge>
                        <div className="text-xs text-zinc-600 font-mono bg-zinc-800/40 px-1.5 py-0.5 rounded text-center min-w-[1.5rem]">
                          {String(index + 1).padStart(2, '0')}
                        </div>
                      </div>
                    </div>

                    {/* Compact Description */}
                    <p className="text-xs text-zinc-400 leading-relaxed mb-3">
                      {API_KEY_CONFIGS[provider as keyof typeof API_KEY_CONFIGS]?.description || "AI provider"}
                    </p>

                    {/* Provider Impact Information */}
                    <div className="mb-3">
                      {(() => {
                        const availableModels = getModelsForProvider(provider);
                        const fallbackModels = getFallbackInfo(provider);
                        const primaryModels = availableModels.filter(m => m.isPrimary);
                        const multiProviderModels = availableModels.filter(m => m.hasMultiProvider);
                        
                        return (
                          <div className="space-y-2">
                            {/* Primary Models */}
                            {primaryModels.length > 0 && (
                              <div className="p-2 bg-gradient-to-r from-blue-900/20 to-blue-800/10 rounded border border-blue-700/30">
                                <div className="flex items-center gap-1.5 mb-1">
                                  <Sparkles className="h-2.5 w-2.5 text-blue-400" />
                                  <span className="text-xs font-medium text-blue-300">
                                    Primary Access ({primaryModels.length})
                                  </span>
                                </div>
                                <div className="flex flex-wrap gap-1">
                                  {primaryModels.slice(0, 3).map((model, idx) => (
                                    <Badge 
                                      key={idx}
                                      className="bg-blue-900/40 text-blue-300 border-blue-700/50 text-xs px-1.5 py-0.5"
                                    >
                                      {model.model}
                                    </Badge>
                                  ))}
                                  {primaryModels.length > 3 && (
                                    <Badge className="bg-blue-900/40 text-blue-300 border-blue-700/50 text-xs px-1.5 py-0.5">
                                      +{primaryModels.length - 3} more
                                    </Badge>
                                  )}
                                </div>
                              </div>
                            )}
                            
                            {/* Multi-Provider Support */}
                            {multiProviderModels.length > 0 && (
                              <div className="p-2 bg-gradient-to-r from-green-900/20 to-emerald-900/10 rounded border border-green-700/30">
                                <div className="flex items-center gap-1.5 mb-1">
                                  <CheckCircle className="h-2.5 w-2.5 text-green-400" />
                                  <span className="text-xs font-medium text-green-300">
                                    Fallback Support ({multiProviderModels.length})
                                  </span>
                                </div>
                                <p className="text-xs text-zinc-400">
                                  Automatic fallback for {multiProviderModels.length} models
                                </p>
                              </div>
                            )}
                            
                            {/* Fallback Information */}
                            {fallbackModels.length > 0 && (
                              <div className="p-2 bg-gradient-to-r from-amber-900/20 to-yellow-900/10 rounded border border-amber-700/30">
                                <div className="flex items-center gap-1.5 mb-1">
                                  <InfinityIcon className="h-2.5 w-2.5 text-amber-400" />
                                  <span className="text-xs font-medium text-amber-300">
                                    Backup Provider ({fallbackModels.length})
                                  </span>
                                </div>
                                <p className="text-xs text-zinc-400">
                                  Fallback when primary providers fail
                                </p>
                              </div>
                            )}
                          </div>
                        );
                      })()}
                    </div>

                    <Separator className="bg-zinc-800/60 mb-3" />

                    {/* Compact Input Section */}
                    <div className="space-y-3">
                      <div className="relative">
                        <Input
                          type={showKeys[provider] ? "text" : "password"}
                          placeholder={API_KEY_CONFIGS[provider as keyof typeof API_KEY_CONFIGS]?.keyPlaceholder || "Enter API key..."}
                          value={apiKeys[provider] ?? ""}
                          onChange={(e) =>
                            setApiKeys({ ...apiKeys, [provider]: e.target.value })
                          }
                          className="pr-10 bg-zinc-900/60 border-zinc-700/60 text-zinc-200 placeholder-zinc-500 focus:border-zinc-600 focus:bg-zinc-900/80 h-9 text-sm transition-all duration-300"
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute right-0 top-0 h-full px-2 hover:bg-zinc-800/60 text-zinc-400 hover:text-zinc-300 transition-all duration-300"
                          onClick={() =>
                            setShowKeys({ ...showKeys, [provider]: !showKeys[provider] })
                          }
                        >
                          {showKeys[provider] ? (
                            <EyeOff className="h-3 w-3" />
                          ) : (
                            <Eye className="h-3 w-3" />
                          )}
                        </Button>
                      </div>

                      {/* Compact Action Buttons */}
                      <div className="flex flex-wrap gap-2">
                        <Button
                          onClick={() =>
                            void handleSave(provider as ApiKeyProvider, apiKeys[provider] ?? "")
                          }
                          disabled={!apiKeys[provider]?.trim()}
                          className="flex-1 sm:flex-none text-xs py-1.5 px-3 bg-gradient-to-r from-zinc-800 to-slate-800 hover:from-zinc-700 hover:to-slate-700 text-white border border-zinc-700/60 hover:border-zinc-600/80 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300"
                        >
                          Save
                        </Button>
                        {hasUserKey && (
                          <Button
                            variant="destructive"
                            onClick={() => void handleRemove(provider as ApiKeyProvider)}
                            className="flex-1 sm:flex-none text-xs py-1.5 px-2 bg-gradient-to-r from-red-900/70 to-red-800/50 hover:from-red-800/80 hover:to-red-700/60 border border-red-700/60 hover:border-red-600/80 transition-all duration-300"
                          >
                            <Trash2 className="mr-1 h-3 w-3" />
                            Remove
                          </Button>
                        )}
                      </div>

                      {/* Compact Key Info */}
                      {hasUserKey && keyInfo?.keyPreview && (
                        <div className="relative">
                          <div className="p-2 bg-gradient-to-r from-green-900/20 to-emerald-900/10 rounded border border-green-700/40">
                            <div className="flex items-center gap-1.5 mb-1">
                              <CheckCircle className="h-2.5 w-2.5 text-green-400" />
                              <span className="text-xs font-medium text-green-300">Active</span>
                            </div>
                            <p className="text-xs text-zinc-500 font-mono">
                              {keyInfo.keyPreview} • {keyInfo.addedAt
                                ? new Date(keyInfo.addedAt).toLocaleDateString()
                                : "Recently"}
                            </p>
                            
                            {/* Enhanced Model Availability Info */}
                            {(() => {
                              const availableModels = getModelsForProvider(provider);
                              const primaryModels = availableModels.filter(m => m.isPrimary);
                              const fallbackModels = availableModels.filter(m => !m.isPrimary && m.hasMultiProvider);
                              
                              return (
                                <div className="mt-2 pt-2 border-t border-green-700/30">
                                  <div className="flex items-center justify-between text-xs">
                                    <span className="text-green-300">
                                      {primaryModels.length} primary • {fallbackModels.length} fallback
                                    </span>
                                    <span className="text-zinc-400">
                                      {availableModels.length} total models
                                    </span>
                                  </div>
                                </div>
                              );
                            })()}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
} 
