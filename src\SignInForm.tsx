// components/SignInForm.tsx
"use client";
import { useAuthActions } from "@convex-dev/auth/react";
import { useState, useEffect } from "react";
import { toast } from "sonner";
import { Mail, KeyRound, LoaderCircle, Github, ArrowRight, Eye, EyeOff } from "lucide-react";

// This component is now just the form card, without any background logic.
export function SignInForm() {
  const { signIn } = useAuthActions();
  const [flow, setFlow] = useState<"signIn" | "signUp">("signIn");
  const [submitting, setSubmitting] = useState(false);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [emailFocused, setEmailFocused] = useState(false);
  const [passwordFocused, setPasswordFocused] = useState(false);
  const [emailTouched, setEmailTouched] = useState(false);
  const [passwordTouched, setPasswordTouched] = useState(false);

  // Validate email format
  const isEmailValid = email === "" || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  const isPasswordValid = password === "" || password.length >= 6;
  const canSubmit = email && password && isEmailValid && isPasswordValid;

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!canSubmit) return;
    
    setSubmitting(true);
    const formData = new FormData(e.target as HTMLFormElement);
    formData.set("flow", flow);

    signIn("password", formData)
      .catch((error) => {
        let toastTitle = "An error occurred";
        if (error.message.includes("Invalid password")) {
          toastTitle = "Invalid password. Please try again.";
        } else {
          toastTitle =
            flow === "signIn"
              ? "Could not sign in. Do you need to sign up?"
              : "Could not sign up. Do you already have an account?";
        }
        toast.error(toastTitle);
      })
      .finally(() => {
        setSubmitting(false);
      });
  };

  const handleOAuthSignIn = (provider: string) => {
    setSubmitting(true);
    signIn(provider as any)
      .catch((error) => {
        toast.error(`Failed to sign in with ${provider}. Please try again.`);
      })
      .finally(() => {
        setSubmitting(false);
      });
  };

  return (
    <div className="w-full space-y-4">
      {/* Flow Toggle */}
      <div className="flex bg-zinc-900/50 rounded-xl p-1 border border-zinc-800/50">
        <button
          type="button"
          onClick={() => setFlow("signIn")}
          className={`flex-1 py-2 px-3 rounded-lg text-sm font-medium transition-all duration-200 ${
            flow === "signIn"
              ? "bg-zinc-800 text-white shadow-lg shadow-zinc-900/50"
              : "text-zinc-400 hover:text-zinc-300"
          }`}
        >
          Sign In
        </button>
        <button
          type="button"
          onClick={() => setFlow("signUp")}
          className={`flex-1 py-2 px-3 rounded-lg text-sm font-medium transition-all duration-200 ${
            flow === "signUp"
              ? "bg-zinc-800 text-white shadow-lg shadow-zinc-900/50"
              : "text-zinc-400 hover:text-zinc-300"
          }`}
        >
          Sign Up
        </button>
      </div>

      <form className="space-y-4" onSubmit={handleSubmit}>
        {/* Email Field */}
        <div className="relative group">
          <div className={`absolute -inset-0.5 bg-gradient-to-r from-zinc-600/0 via-zinc-500/30 to-zinc-600/0 rounded-xl opacity-0 blur-sm transition-opacity duration-300 ${
            emailFocused ? "opacity-100" : ""
          }`} />
          <div className={`relative bg-zinc-900/60 border rounded-xl transition-all duration-200 ${
            emailFocused
              ? "border-zinc-600/80 bg-zinc-900/80"
              : "border-zinc-800/60 hover:border-zinc-700/70"
          } ${!isEmailValid && emailTouched ? "border-red-500/50" : ""}`}>
            <div className="relative">
              <Mail className={`absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 transition-colors duration-200 ${
                emailFocused ? "text-zinc-300" : "text-zinc-500"
              }`} />
              <input
                id="email"
                type="email"
                name="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                onFocus={() => setEmailFocused(true)}
                onBlur={() => {
                  setEmailFocused(false);
                  setEmailTouched(true);
                }}
                className="w-full pl-10 pr-3 py-3 bg-transparent text-white placeholder-zinc-500 focus:outline-none text-sm"
                placeholder="Enter your email"
                required
              />
              {email && (
                <div className={`absolute right-3 top-1/2 -translate-y-1/2 w-2 h-2 rounded-full transition-colors duration-200 ${
                  isEmailValid ? "bg-emerald-500" : "bg-red-500"
                }`} />
              )}
            </div>
          </div>
          {!isEmailValid && emailTouched && (
            <p className="text-red-400 text-xs mt-1 ml-1 animate-fade-in">Please enter a valid email address</p>
          )}
        </div>

        {/* Password Field */}
        <div className="relative group">
          <div className={`absolute -inset-0.5 bg-gradient-to-r from-zinc-600/0 via-zinc-500/30 to-zinc-600/0 rounded-xl opacity-0 blur-sm transition-opacity duration-300 ${
            passwordFocused ? "opacity-100" : ""
          }`} />
          <div className={`relative bg-zinc-900/60 border rounded-xl transition-all duration-200 ${
            passwordFocused
              ? "border-zinc-600/80 bg-zinc-900/80"
              : "border-zinc-800/60 hover:border-zinc-700/70"
          } ${!isPasswordValid && passwordTouched ? "border-red-500/50" : ""}`}>
            <div className="relative">
              <KeyRound className={`absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 transition-colors duration-200 ${
                passwordFocused ? "text-zinc-300" : "text-zinc-500"
              }`} />
              <input
                id="password"
                type={showPassword ? "text" : "password"}
                name="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                onFocus={() => setPasswordFocused(true)}
                onBlur={() => {
                  setPasswordFocused(false);
                  setPasswordTouched(true);
                }}
                className="w-full pl-10 pr-10 py-3 bg-transparent text-white placeholder-zinc-500 focus:outline-none text-sm"
                placeholder="Enter your password"
                required
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-zinc-500 hover:text-zinc-300 transition-colors duration-200"
              >
                {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </button>
            </div>
          </div>
          {!isPasswordValid && passwordTouched && (
            <p className="text-red-400 text-xs mt-1 ml-1 animate-fade-in">Password must be at least 6 characters</p>
          )}
        </div>

        {/* Submit Button */}
        <button
          type="submit"
          disabled={submitting || !canSubmit}
          className={`w-full relative group overflow-hidden rounded-xl transition-all duration-300 ${
            canSubmit && !submitting
              ? "bg-gradient-to-r from-zinc-700 to-slate-700 hover:from-zinc-600 hover:to-slate-600 transform hover:scale-[1.02] shadow-xl hover:shadow-2xl"
              : "bg-zinc-800/50 cursor-not-allowed"
          }`}
        >
          {/* Button glow effect */}
          <div className={`absolute -inset-0.5 bg-gradient-to-r from-zinc-600/0 via-zinc-400/40 to-zinc-600/0 rounded-xl opacity-0 blur-sm transition-opacity duration-300 ${
            canSubmit && !submitting ? "group-hover:opacity-100" : ""
          }`} />
          
          <div className="relative flex items-center justify-center py-3 px-4">
            {submitting ? (
              <LoaderCircle className="animate-spin h-4 w-4 text-white" />
            ) : (
              <>
                <span className="text-white font-semibold text-sm">
                  {flow === "signIn" ? "Sign In" : "Create Account"}
                </span>
                <ArrowRight className={`ml-2 h-4 w-4 text-white transition-transform duration-300 ${
                  canSubmit ? "group-hover:translate-x-1" : ""
                }`} />
              </>
            )}
          </div>
        </button>
      </form>

      {/* Divider */}
      <div className="relative flex items-center">
        <div className="flex-grow h-px bg-gradient-to-r from-transparent via-zinc-700/50 to-transparent" />
        <div className="relative px-3">
          <div className="bg-zinc-900/80 px-2 py-1 rounded-full border border-zinc-800/50">
            <span className="text-xs font-medium text-zinc-500 uppercase tracking-wider">Or continue with</span>
          </div>
        </div>
        <div className="flex-grow h-px bg-gradient-to-r from-transparent via-zinc-700/50 to-transparent" />
      </div>

      {/* OAuth Buttons */}
      <div className="grid grid-cols-2 gap-3">
        <button
          type="button"
          onClick={() => handleOAuthSignIn("google")}
          disabled={submitting}
          className="relative group bg-white hover:bg-gray-50 border border-gray-200 rounded-xl py-3 px-3 transition-all duration-200 hover:scale-[1.02] hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <div className="flex items-center justify-center gap-2">
            <svg className="h-4 w-4 flex-shrink-0" viewBox="0 0 24 24">
              <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
              <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
              <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
              <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
            </svg>
            <span className="text-gray-700 font-medium text-sm">Google</span>
          </div>
        </button>

        <button
          type="button"
          onClick={() => handleOAuthSignIn("github")}
          disabled={submitting}
          className="relative group bg-zinc-900 hover:bg-zinc-800 border border-zinc-700 rounded-xl py-3 px-3 transition-all duration-200 hover:scale-[1.02] hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <div className="flex items-center justify-center gap-2">
            <Github className="h-4 w-4 text-white flex-shrink-0" />
            <span className="text-white font-medium text-sm">GitHub</span>
          </div>
        </button>
      </div>

      {/* Helper Text */}
      <div className="text-center pt-2">
        <p className="text-xs text-zinc-500 leading-relaxed">
          By continuing, you agree to our{" "}
          <a href="/terms" className="text-zinc-400 hover:text-zinc-300 transition-colors">Terms</a>
          {" "}and{" "}
          <a href="/privacy" className="text-zinc-400 hover:text-zinc-300 transition-colors">Privacy Policy</a>
        </p>
      </div>
    </div>
  );
}