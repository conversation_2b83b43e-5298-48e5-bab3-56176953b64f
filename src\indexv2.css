@import url('https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap');
@import "tailwindcss";

@custom-variant dark (&:is(.dark *));
@config "../tailwind.config.cjs";

@layer base {
  :root {
    /* Modern Light Theme - Neutral Gray */
    --background: 0 0% 100%;
    --foreground: 0 0% 10%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 10%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 10%;
    --primary: 0 0% 20%;
    --primary-foreground: 0 0% 100%;
    --secondary: 0 0% 97.5%;
    --secondary-foreground: 0 0% 10%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 95%;
    --accent-foreground: 0 0% 20%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 20%;
    --radius: 0.75rem;
    
    /* Chat-specific colors */
    --chat-background: 0 0% 100%;
    --chat-user: 0 0% 95%;
    --chat-assistant: 0 0% 100%;
    --chat-border: 0 0% 89.8%;
    
    /* Sidebar colors */
    --sidebar: 0 0% 98%;
    --sidebar-foreground: 0 0% 10%;
    --sidebar-primary: 0 0% 20%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 0 0% 94%;
    --sidebar-accent-foreground: 0 0% 10%;
    --sidebar-border: 0 0% 89.8%;
    --sidebar-ring: 0 0% 20%;

    /* Typography Scale - Based on 16px base with 1.25 ratio */
    --font-size-xs: 0.75rem;    /* 12px */
    --font-size-sm: 0.875rem;   /* 14px */
    --font-size-base: 1rem;     /* 16px */
    --font-size-lg: 1.125rem;   /* 18px */
    --font-size-xl: 1.25rem;    /* 20px */
    --font-size-2xl: 1.5rem;    /* 24px */
    --font-size-3xl: 1.875rem;  /* 30px */
    --font-size-4xl: 2.25rem;   /* 36px */
    --font-size-5xl: 3rem;      /* 48px */
    --font-size-6xl: 3.75rem;   /* 60px */
    --font-size-7xl: 4.5rem;    /* 72px */
    --font-size-8xl: 6rem;      /* 96px */
    --font-size-9xl: 8rem;      /* 128px */

    /* Font Weights */
    --font-thin: 100;
    --font-extralight: 200;
    --font-light: 300;
    --font-normal: 400;
    --font-medium: 500;
    --font-semibold: 600;
    --font-bold: 700;
    --font-extrabold: 800;
    --font-black: 900;
    
    /* Line Heights */
    --leading-none: 1;
    --leading-tight: 1.25;
    --leading-snug: 1.375;
    --leading-normal: 1.5;
    --leading-relaxed: 1.625;
    --leading-loose: 2;
    
    /* Letter Spacing */
    --tracking-tighter: -0.05em;
    --tracking-tight: -0.025em;
    --tracking-normal: 0em;
    --tracking-wide: 0.025em;
    --tracking-wider: 0.05em;
    --tracking-widest: 0.1em;
    
    /* Font Families */
    --font-sans: 'Open Sans Variable', 'Open Sans', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
    
    /* Legacy line height names for compatibility */
    --line-height-tight: var(--leading-tight);
    --line-height-normal: var(--leading-normal);
    --line-height-relaxed: var(--leading-relaxed);
    
    /* Spacing Scale */
    --spacing-xs: 0.25rem;    /* 4px */
    --spacing-sm: 0.5rem;     /* 8px */
    --spacing-md: 1rem;       /* 16px */
    --spacing-lg: 1.5rem;     /* 24px */
    --spacing-xl: 2rem;       /* 32px */
    --spacing-2xl: 3rem;      /* 48px */
    
    /* Shadow Scale */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    
    /* Transitions & Animations */
    --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-base: 200ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 300ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: 500ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
    
    /* Backdrop filters */
    --backdrop-blur: blur(10px);
    --backdrop-saturate: saturate(180%);
    
    /* Z-index scale */
    --z-dropdown: 50;
    --z-sticky: 100;
    --z-overlay: 200;
    --z-modal: 300;
    --z-popover: 400;
    --z-tooltip: 500;
  }

  .dark {
    /* Modern Dark Theme - Neutral Gray */
    --background: 212 11% 14%;
    --foreground: 213 27% 84%;
    --card: 215 14% 11%;
    --card-foreground: 213 27% 84%;
    --popover: 215 14% 11%;
    --popover-foreground: 213 27% 84%;
    --primary: 0 0% 80%;
    --primary-foreground: 0 0% 10%;
    --secondary: 215 14% 20%;
    --secondary-foreground: 213 27% 84%;
    --muted: 215 14% 18%;
    --muted-foreground: 215 13% 56%;
    --accent: 0 0% 22%;
    --accent-foreground: 0 0% 80%;
    --destructive: 0 62% 45%;
    --destructive-foreground: 210 40% 98%;
    --border: 215 14% 25%;
    --input: 215 14% 25%;
    --ring: 0 0% 80%;
    
    /* Chat-specific colors */
    --chat-background: 212 11% 14%;
    --chat-user: 212 11% 16%;
    --chat-assistant: 215 14% 11%;
    --chat-border: 215 14% 25%;
    
    /* Sidebar colors */
    --sidebar: 215 14% 11%;
    --sidebar-foreground: 213 27% 84%;
    --sidebar-primary: 0 0% 80%;
    --sidebar-primary-foreground: 0 0% 10%;
    --sidebar-accent: 215 14% 16%;
    --sidebar-accent-foreground: 213 27% 84%;
    --sidebar-border: 215 14% 25%;
    --sidebar-ring: 0 0% 80%;

    /* Dark mode shadows with subtle transparency */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.3), 0 2px 4px -2px rgb(0 0 0 / 0.3);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.3), 0 4px 6px -4px rgb(0 0 0 / 0.3);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.3), 0 8px 10px -6px rgb(0 0 0 / 0.3);
  }

  /* Base styles */
  * {
    border-color: hsl(var(--border));
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    font-family: var(--font-sans);
    font-feature-settings: "rlig" 1, "calt" 1, "ss01" 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Typography base styles */
  h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    color: hsl(var(--foreground));
    line-height: var(--line-height-tight);
  }

  p {
    line-height: var(--line-height-normal);
  }

  /* Focus styles */
  :focus-visible {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
    border-radius: calc(var(--radius) - 2px);
  }

  /* Selection styles */
  ::selection {
    background-color: hsl(var(--primary) / 0.1);
    color: hsl(var(--primary));
  }
  
  /* Scrollbar styles */
  ::-webkit-scrollbar {
    width: 10px;
    height: 10px;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-transparent;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-border rounded-full;
    border: 2px solid transparent;
    background-clip: padding-box;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/30;
  }
  
  /* Firefox scrollbar */
  * {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--border)) transparent;
  }
}

@layer components {
  /* Note: Using shadcn Button component instead of custom btn classes */
  
  /* Note: These components should use shadcn components or inline Tailwind classes instead of @apply */
  /* Components have been moved to use shadcn/ui components for better Tailwind v4 compatibility */

  /* Modern glass effect for special components */
  .glass-effect {
    @apply bg-background/80 backdrop-blur-md border border-border/50;
  }

  .dark .glass-effect {
    @apply bg-background/60 backdrop-blur-lg border-border/30;
  }
}

/* Prose/Markdown content styling */
@layer utilities {
  .prose {
    color: hsl(var(--foreground));
    max-width: none;
  }

  .prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
    color: hsl(var(--foreground));
    font-weight: 600;
    margin-top: 1.5em;
    margin-bottom: 0.5em;
    line-height: var(--line-height-tight);
  }

  .prose h1 { font-size: var(--font-size-2xl); }
  .prose h2 { font-size: var(--font-size-xl); }
  .prose h3 { font-size: var(--font-size-lg); }
  .prose h4 { font-size: var(--font-size-base); }

  .prose p {
    margin-top: 1em;
    margin-bottom: 1em;
    line-height: var(--line-height-normal);
  }

  .prose a {
    color: hsl(var(--primary));
    text-decoration: underline;
    text-underline-offset: 2px;
  }

  .prose a:hover {
    color: hsl(var(--primary) / 0.8);
  }

  .prose strong {
    font-weight: 600;
    color: hsl(var(--foreground));
  }

  .prose em {
    font-style: italic;
    color: hsl(var(--foreground) / 0.9);
  }

  .prose code:not(pre code) {
    background: hsl(var(--muted));
    color: hsl(var(--muted-foreground));
    padding: 0.125rem 0.25rem;
    border-radius: calc(var(--radius) - 2px);
    font-family: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;
    font-size: 0.875em;
  }

  .prose pre {
    background: hsl(var(--muted));
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius);
    padding: 1rem;
    overflow-x: auto;
    margin: 1em 0;
  }

  .prose pre code {
    background: transparent;
    padding: 0;
    border-radius: 0;
    font-family: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;
    color: hsl(var(--foreground));
  }

  .prose blockquote {
    border-left: 4px solid hsl(var(--primary));
    padding-left: 1rem;
    margin: 1.5em 0;
    font-style: italic;
    color: hsl(var(--muted-foreground));
  }

  .prose ul, .prose ol {
    margin: 1em 0;
    padding-left: 1.5rem;
  }

  .prose li {
    margin: 0.5em 0;
  }

  .prose table {
    width: 100%;
    border-collapse: collapse;
    margin: 1em 0;
    border: 1px solid hsl(var(--border));
    border-radius: var(--radius);
    overflow: hidden;
  }

  .prose th, .prose td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid hsl(var(--border));
  }

  .prose th {
    background: hsl(var(--muted));
    font-weight: 600;
  }

  .prose tr:nth-child(even) {
    background: hsl(var(--muted) / 0.3);
  }

  /* Selection styles in code blocks */
  .prose pre ::selection,
  .prose code ::selection {
    background: hsl(var(--primary) / 0.3);
  }
}

/* Modern Animation Keyframes */
@keyframes fadeIn {
  from { 
    opacity: 0;
    transform: translateY(4px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes shimmer {
  0% {
    background-position: -1000px 0;
  }
  100% {
    background-position: 1000px 0;
  }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-25%);
  }
}

/* Utility Classes */
.animate-fadeIn {
  animation: fadeIn var(--transition-slow) ease-out;
}

.animate-slideInUp {
  animation: slideInUp var(--transition-slow) ease-out;
}

.animate-slideInDown {
  animation: slideInDown var(--transition-slow) ease-out;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-bounce {
  animation: bounce 1s infinite;
}

/* Skeleton Loading Effect */
.skeleton {
  position: relative;
  overflow: hidden;
  background-color: hsl(var(--muted));
  background: linear-gradient(
    90deg,
    hsl(var(--muted)) 0%,
    hsl(var(--muted) / 0.7) 50%,
    hsl(var(--muted)) 100%
  );
  background-size: 2000px 100%;
  animation: shimmer 2s linear infinite;
}

/* Smooth transitions for interactive elements */
.transition-all {
  transition: all var(--transition-base);
}

.transition-colors {
  transition: color var(--transition-fast), background-color var(--transition-fast), border-color var(--transition-fast);
}

.transition-opacity {
  transition: opacity var(--transition-base);
}

.transition-transform {
  transition: transform var(--transition-base);
}

/* Hover Effects */
.hover-lift {
  transition: transform var(--transition-base), box-shadow var(--transition-base);
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* Modern Glassmorphism */
.glass {
  background: hsl(var(--background) / 0.7);
  backdrop-filter: var(--backdrop-blur) var(--backdrop-saturate);
  border: 1px solid hsl(var(--border) / 0.5);
}

.dark .glass {
  background: hsl(var(--background) / 0.5);
  border: 1px solid hsl(var(--border) / 0.3);
}

/* Gradient Utilities */
.gradient-primary {
  background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary) / 0.8));
}

.gradient-text {
  background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--accent-foreground)));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Additional Modern Utilities */
.text-gradient {
  background: linear-gradient(to right, hsl(var(--primary)), hsl(var(--accent-foreground)));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

.shadow-elevation-low {
  box-shadow: 
    0 1px 2px 0 rgba(0, 0, 0, 0.05),
    0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.shadow-elevation-medium {
  box-shadow: 
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.shadow-elevation-high {
  box-shadow: 
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Smooth transitions for theme switching */
* {
  transition: 
    background-color var(--duration-fast) ease,
    border-color var(--duration-fast) ease,
    color var(--duration-fast) ease,
    box-shadow var(--duration-fast) ease;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--background));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.5);
}

/* Firefox scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--muted-foreground) / 0.3) hsl(var(--background));
}

/* Print styles */
@media print {
  * {
    -webkit-print-color-adjust: exact !important;
    color-adjust: exact !important;
  }
  
  .prose {
    font-size: 12pt;
    line-height: 1.4;
  }
  
  .prose h1, .prose h2, .prose h3 {
    break-after: avoid;
  }
  
  .prose pre, .prose blockquote {
    break-inside: avoid;
  }
}

/* ========== COLOR THEMES ========== */
/* Each color theme supports both light and dark modes */

/* Blue Theme */
.theme-blue {
  --primary: 214 100% 59%;
  --primary-foreground: 0 0% 100%;
  --accent: 214 100% 95%;
  --accent-foreground: 214 100% 30%;
  --ring: 214 100% 59%;
  --sidebar-primary: 214 100% 59%;
  --sidebar-ring: 214 100% 59%;
}

.theme-blue.dark {
  --primary: 214 100% 65%;
  --accent: 214 100% 15%;
  --accent-foreground: 214 100% 70%;
  --sidebar-primary: 214 100% 65%;
}

/* Green Theme */
.theme-green {
  --primary: 142 76% 36%;
  --primary-foreground: 0 0% 100%;
  --accent: 142 76% 95%;
  --accent-foreground: 142 76% 20%;
  --ring: 142 76% 36%;
  --sidebar-primary: 142 76% 36%;
  --sidebar-ring: 142 76% 36%;
}

.theme-green.dark {
  --primary: 142 76% 50%;
  --accent: 142 76% 12%;
  --accent-foreground: 142 76% 60%;
  --sidebar-primary: 142 76% 50%;
}

/* Purple Theme */
.theme-purple {
  --primary: 262 83% 58%;
  --primary-foreground: 0 0% 100%;
  --accent: 262 83% 95%;
  --accent-foreground: 262 83% 30%;
  --ring: 262 83% 58%;
  --sidebar-primary: 262 83% 58%;
  --sidebar-ring: 262 83% 58%;
}

.theme-purple.dark {
  --primary: 262 83% 65%;
  --accent: 262 83% 15%;
  --accent-foreground: 262 83% 70%;
  --sidebar-primary: 262 83% 65%;
}

/* Orange Theme */
.theme-orange {
  --primary: 24 95% 53%;
  --primary-foreground: 0 0% 100%;
  --accent: 24 95% 95%;
  --accent-foreground: 24 95% 25%;
  --ring: 24 95% 53%;
  --sidebar-primary: 24 95% 53%;
  --sidebar-ring: 24 95% 53%;
}

.theme-orange.dark {
  --primary: 24 95% 60%;
  --accent: 24 95% 12%;
  --accent-foreground: 24 95% 65%;
  --sidebar-primary: 24 95% 60%;
}

/* Pink Theme */
.theme-pink {
  --primary: 330 81% 60%;
  --primary-foreground: 0 0% 100%;
  --accent: 330 81% 95%;
  --accent-foreground: 330 81% 30%;
  --ring: 330 81% 60%;
  --sidebar-primary: 330 81% 60%;
  --sidebar-ring: 330 81% 60%;
}

.theme-pink.dark {
  --primary: 330 81% 65%;
  --accent: 330 81% 15%;
  --accent-foreground: 330 81% 70%;
  --sidebar-primary: 330 81% 65%;
}

/* Teal Theme */
.theme-teal {
  --primary: 173 58% 39%;
  --primary-foreground: 0 0% 100%;
  --accent: 173 58% 95%;
  --accent-foreground: 173 58% 25%;
  --ring: 173 58% 39%;
  --sidebar-primary: 173 58% 39%;
  --sidebar-ring: 173 58% 39%;
}

.theme-teal.dark {
  --primary: 173 58% 50%;
  --accent: 173 58% 12%;
  --accent-foreground: 173 58% 55%;
  --sidebar-primary: 173 58% 50%;
}

/* Red Theme */
.theme-red {
  --primary: 0 72% 51%;
  --primary-foreground: 0 0% 100%;
  --accent: 0 72% 95%;
  --accent-foreground: 0 72% 30%;
  --ring: 0 72% 51%;
  --sidebar-primary: 0 72% 51%;
  --sidebar-ring: 0 72% 51%;
}

.theme-red.dark {
  --primary: 0 72% 60%;
  --accent: 0 72% 15%;
  --accent-foreground: 0 72% 65%;
  --sidebar-primary: 0 72% 60%;
}

/* Indigo Theme */
.theme-indigo {
  --primary: 239 84% 67%;
  --primary-foreground: 0 0% 100%;
  --accent: 239 84% 95%;
  --accent-foreground: 239 84% 35%;
  --ring: 239 84% 67%;
  --sidebar-primary: 239 84% 67%;
  --sidebar-ring: 239 84% 67%;
}

.theme-indigo.dark {
  --primary: 239 84% 70%;
  --accent: 239 84% 15%;
  --accent-foreground: 239 84% 75%;
  --sidebar-primary: 239 84% 70%;
}

/* Sunset Theme - Orange to Pink gradient effect */
.theme-sunset {
  --primary: 17 88% 55%;
  --primary-foreground: 0 0% 100%;
  --accent: 17 88% 95%;
  --accent-foreground: 17 88% 30%;
  --ring: 17 88% 55%;
  --sidebar-primary: 17 88% 55%;
  --sidebar-ring: 17 88% 55%;
  --secondary: 330 81% 95%;
}

.theme-sunset.dark {
  --primary: 17 88% 62%;
  --accent: 17 88% 12%;
  --accent-foreground: 17 88% 67%;
  --sidebar-primary: 17 88% 62%;
  --secondary: 330 81% 15%;
}

/* Ocean Theme - Cyan to Sky blue */
.theme-ocean {
  --primary: 188 94% 43%;
  --primary-foreground: 0 0% 100%;
  --accent: 188 94% 95%;
  --accent-foreground: 188 94% 25%;
  --ring: 188 94% 43%;
  --sidebar-primary: 188 94% 43%;
  --sidebar-ring: 188 94% 43%;
}

.theme-ocean.dark {
  --primary: 188 94% 55%;
  --accent: 188 94% 12%;
  --accent-foreground: 188 94% 60%;
  --sidebar-primary: 188 94% 55%;
}

/* Forest Theme - Emerald to Lime */
.theme-forest {
  --primary: 160 84% 39%;
  --primary-foreground: 0 0% 100%;
  --accent: 160 84% 95%;
  --accent-foreground: 160 84% 25%;
  --ring: 160 84% 39%;
  --sidebar-primary: 160 84% 39%;
  --sidebar-ring: 160 84% 39%;
}

.theme-forest.dark {
  --primary: 160 84% 50%;
  --accent: 160 84% 12%;
  --accent-foreground: 160 84% 55%;
  --sidebar-primary: 160 84% 50%;
}

/* Gold Theme - Amber to Yellow */
.theme-gold {
  --primary: 45 93% 47%;
  --primary-foreground: 0 0% 100%;
  --accent: 45 93% 95%;
  --accent-foreground: 45 93% 25%;
  --ring: 45 93% 47%;
  --sidebar-primary: 45 93% 47%;
  --sidebar-ring: 45 93% 47%;
}

.theme-gold.dark {
  --primary: 45 93% 55%;
  --accent: 45 93% 12%;
  --accent-foreground: 45 93% 60%;
  --sidebar-primary: 45 93% 55%;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --border: 240 10% 20%;
    --input: 240 10% 20%;
  }
  
  .dark {
    --border: 210 40% 80%;
    --input: 210 40% 80%;
  }
}
