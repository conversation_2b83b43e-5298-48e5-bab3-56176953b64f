import { Wrench, Terminal, Copy, Edit2, RotateCcw, GitBranch, Clock, Zap, Database, Check, X, ChevronDown, Brain, Search, Calculator, CloudRain, FileText, Code, Link, BookOpen, Thermometer, Droplets, Wind, Gauge, Eye, Sun, Cloudy } from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkMath from 'remark-math';
import remarkEmoji from 'remark-emoji';
import remarkDirective from 'remark-directive';
import remarkFrontmatter from 'remark-frontmatter';
import remarkWikiLink from 'remark-wiki-link';
import rehypeKatex from 'rehype-katex';
import rehypeSlug from 'rehype-slug';
import rehypeAutolinkHeadings from 'rehype-autolink-headings';
import rehypeRaw from 'rehype-raw'; // Allow rendering of raw HTML inside markdown
import remarkBreaks from 'remark-breaks';
import rehypeUnwrapImages from 'rehype-unwrap-images';
import rehypeSanitize from 'rehype-sanitize';
import dedent from 'dedent';

import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { oneDark, oneLight } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { useTheme } from 'next-themes';
import { cn } from '@/lib/utils';
import { getUltraMinimalError } from '@/lib/errorUtils';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { LoadingDots } from '@/components/ui/loading-dots';
import { useState, useMemo, memo, useCallback, useEffect } from 'react';
import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import 'katex/dist/katex.min.css';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuGroup,
  DropdownMenuLabel
} from "@/components/ui/dropdown-menu";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { Canvas } from "./Canvas";
import mermaid from 'mermaid';
import { useOptimizedTimer } from '@/hooks/useOptimizedTimer';
import { useDebounce } from '@/hooks/useDebounce';
import { LazyImage } from '@/components/LazyImage';
import TruncatedContent from '@/components/TruncatedContent';
import { AttachmentPlaceholder } from '@/components/AttachmentPlaceholder';
import { getModelsByGroup } from "@/lib/models";
import { Tooltip, TooltipTrigger, TooltipContent } from '@/components/ui/tooltip';

const Mermaid = memo(({ chart }: { chart: string }) => {
    const { theme } = useTheme();
    const [svg, setSvg] = useState<string | null>(null);
    const [error, setError] = useState<string | null>(null);
    
    useEffect(() => {
        mermaid.initialize({
            startOnLoad: false,
            theme: theme === 'dark' ? 'dark' : 'forest', // forest is a nice dark theme
            securityLevel: 'loose',
            fontFamily: 'inherit',
            // Suppress Mermaid's default error overlay/banner
            parseError: () => {
              /* no-op to prevent default error UI */
            },
        } as any);

        const id = `mermaid-svg-${Math.random().toString(36).substring(2, 9)}`;
        
        setSvg(null);
        setError(null);

        try {
            mermaid.render(id, chart)
                .then(({ svg }) => {
                    setSvg(svg);
                })
                .catch((e) => {
                    console.error("Mermaid rendering error (async):", e);
                    // Remove mermaid's injected error banner if present
                    const errEl = document.querySelector(`#d${id}`);
                    if (errEl && errEl.parentElement) {
                      errEl.parentElement.removeChild(errEl);
                    }
                    const errorMessage = e instanceof Error ? e.message : String(e);
                    const cleanError = errorMessage.split('\n')[0];
                    setError(cleanError || "Error rendering diagram.");
                });
        } catch (e) {
            console.error("Mermaid rendering error (sync):", e);
            const errEl = document.querySelector(`#d${id}`);
            if (errEl && errEl.parentElement) {
              errEl.parentElement.removeChild(errEl);
            }
            const errorMessage = e instanceof Error ? e.message : String(e);
            const cleanError = errorMessage.split('\n')[0];
            setError(cleanError || "Error rendering diagram.");
        }
    }, [chart, theme]);

    if (error) {
        return (
            <div className="mermaid-container my-6 p-4 flex flex-col justify-center items-center bg-red-50/50 dark:bg-red-900/20 rounded-lg border-2 border-dashed border-red-300 dark:border-red-700/50 shadow-sm min-h-[100px]">
                <p className="font-semibold text-red-700 dark:text-red-300 mb-2">Diagram Error</p>
                <pre className="text-xs text-red-600 dark:text-red-400 whitespace-pre-wrap text-center">{error}</pre>
            </div>
        );
    }

    if (svg) {
        return <div className="mermaid-container my-6 p-4 flex justify-center items-center bg-card rounded-lg border border-border/50 shadow-sm" dangerouslySetInnerHTML={{ __html: svg }} />;
    }

    return (
        <div className="my-6 p-4 flex justify-center items-center bg-card rounded-lg border border-border/50 shadow-sm min-h-[100px]">
            <p className="text-muted-foreground animate-pulse">Rendering diagram...</p>
        </div>
    );
});
Mermaid.displayName = 'Mermaid';

interface MessageBubbleProps {
  message: {
    _id: Id<"messages">;
    role: "user" | "assistant" | "system" | "tool";
    content: string;
    thinking?: string;
    attachments?: any[];
    toolCalls?: any[];
    toolCallId?: string;
    _creationTime: number;
    conversationId: Id<"conversations">;
    branchId?: string;
    parentMessageId?: Id<"messages">;
    generationMetrics?: {
      provider?: string;
      model?: string;
      tokensUsed?: number;
      promptTokens?: number;
      completionTokens?: number;
      generationTimeMs?: number;
      tokensPerSecond?: number;
      temperature?: number;
      maxOutputTokens?: number;
      timeToFirstTokenMs?: number;
      timeToFirstContentMs?: number;
      reasoningTimeMs?: number;
      toolExecutionTimeMs?: number;
    };
    isEdited?: boolean;
    editedAt?: number;
    isError?: boolean;
    canvasData?:
      | {
          type: "markdown" | "code" | "chart" | "react";
          title: string;
          content: string;
          language?: string;
          chartSpec?: string;
          library?: "chartjs" | "echarts" | "d3";
          updatedAt: number;
        }
      | Array<{
          type: "markdown" | "code" | "chart" | "react";
          title: string;
          content: string;
          language?: string;
          chartSpec?: string;
          library?: "chartjs" | "echarts" | "d3";
          updatedAt: number;
        }>;
    isOutdatedCanvas?: boolean;
  };
  messagePosition?: number; // Position of this message in the conversation
  currentBranchId?: string; // Current active branch
  isStreaming?: boolean; // Whether this message is currently being streamed
  isSharedView?: boolean; // Whether this is being displayed in a shared conversation (hides interactive buttons)
  onCopyMessage?: (messageId: Id<"messages">) => void | Promise<void>;
  onEditMessage?: (messageId: Id<"messages">, newContent: string) => void | Promise<void>;
  onRetryMessage?: (messageId: Id<"messages">) => void | Promise<void>;
  onBranchOff?: (messageId: Id<"messages">, provider?: string, model?: string) => void | Promise<void>;
  onSwitchBranch?: (branchId: string) => void;
}

interface ToolCall {
  id: string;
  name: string;
  arguments: string;
  result?: string;
  startTime?: number;
  endTime?: number;
}

// Helper function to group tool calls by name
function groupToolCalls(toolCalls: ToolCall[]): { name: string; count: number; calls: ToolCall[] }[] {
  if (!toolCalls) return [];
  
  const groups: Record<string, { name: string; count: number; calls: ToolCall[] }> = {};

  for (const call of toolCalls) {
    if (!groups[call.name]) {
      groups[call.name] = {
        name: call.name,
        count: 0,
        calls: [],
      };
    }
    groups[call.name].count++;
    groups[call.name].calls.push(call);
  }

  return Object.values(groups);
}

const SYNTAX_HIGHLIGHTER_STYLE = {
  margin: 0,
  padding: '1rem',
  background: 'transparent',
  fontSize: '0.875rem',
  lineHeight: '1.6',
};

const LINE_NUMBER_STYLE = {
  minWidth: '2.5em',
  paddingRight: '0.75em',
  color: 'rgb(var(--muted-foreground))',
  marginRight: '0.75em',
  fontSize: '0.75rem',
};

const CodeBlock = memo(({ className, children, ...props }: any) => {
  const { theme } = useTheme();
  const match = /language-(\w+)/.exec(className || '');
  const language = match ? match[1] : '';
  const isInline = !className || !match;
  
  // Get the raw content without processing
  const rawContent = String(children);
  const codeContent = isInline ? rawContent : rawContent.replace(/\n$/, '');

  // Handle mermaid diagrams
  if (language === 'mermaid') {
    return <Mermaid chart={codeContent} />;
  }

  const handleCopy = () => {
    navigator.clipboard.writeText(codeContent).catch(err => {
      console.error('Failed to copy code:', err);
    });
  };

  // Code blocks (have language class and are multiline)
  if (match && !isInline) {
    return (
      <div className="my-6 rounded-lg bg-card border border-border/50 overflow-hidden shadow-sm w-full">
        <div className="flex items-center justify-between px-4 py-2 bg-muted/30 border-b border-border/30">
          <span className="text-xs font-medium text-muted-foreground uppercase tracking-wider">
            {language}
          </span>
          <button 
            className="text-xs text-muted-foreground hover:text-foreground transition-all duration-200 px-3 py-1.5 rounded-md hover:bg-background/80 border border-transparent hover:border-border/50 flex items-center gap-1.5"
            onClick={handleCopy}
          >
            <Copy size={12} />
            Copy
          </button>
        </div>
        <div className="relative overflow-x-auto">
          <SyntaxHighlighter
            language={language}
            style={theme === 'dark' ? oneDark : oneLight}
            customStyle={SYNTAX_HIGHLIGHTER_STYLE}
            wrapLines={true}
            wrapLongLines={true}
            showLineNumbers={codeContent.split('\n').length > 5}
            lineNumberStyle={LINE_NUMBER_STYLE}
          >
            {codeContent}
          </SyntaxHighlighter>
        </div>
      </div>
    );
  }

  // Inline code - no processing, preserve exact content
  return (
    <code 
      className={cn(
        "component-inline-code text-base bg-muted/70 text-foreground rounded px-1.5 py-0.5 font-mono font-medium",
        "inline whitespace-nowrap"
      )} 
      {...props}
    >
      {rawContent}
    </code>
  );
});

// Memoized markdown components to prevent re-creation on every render
const createMarkdownComponents = () => ({
  code: CodeBlock,
  pre: ({ children, ...props }: any) => (
    <div className="code-block-wrapper">{children}</div>
  ),
  h1: ({ children, ...props }: any) => (
    <h1 {...props} className="text-headline-xl first:mt-0">
      {children}
    </h1>
  ),
  h2: ({ children, ...props }: any) => (
    <h2 {...props} className="text-headline-lg first:mt-0">
      {children}
    </h2>
  ),
  h3: ({ children, ...props }: any) => (
    <h3 {...props} className="text-headline-md first:mt-0">
      {children}
    </h3>
  ),
  h4: ({ children, ...props }: any) => (
    <h4 {...props} className="text-headline-sm first:mt-0">
      {children}
    </h4>
  ),
  h5: ({ children, ...props }: any) => (
    <h5 {...props} className="text-label-xl first:mt-0">
      {children}
    </h5>
  ),
  h6: ({ children, ...props }: any) => (
    <h6 {...props} className="text-label-lg first:mt-0">
      {children}
    </h6>
  ),
  p: ({ children, ...props }: any) => (
    <p {...props}>
      {children}
    </p>
  ),
  ul: ({ children, ...props }: any) => (
    <ul {...props}>
      {children}
    </ul>
  ),
  ol: ({ children, ...props }: any) => (
    <ol {...props}>
      {children}
    </ol>
  ),
  li: ({ children, ...props }: any) => (
    <li {...props}>
      {children}
    </li>
  ),
  blockquote: ({ children, ...props }: any) => (
    <blockquote {...props}>
      {children}
    </blockquote>
  ),
  table: ({ children, ...props }: any) => (
    <table {...props}>
      {children}
    </table>
  ),
  thead: ({ children, ...props }: any) => (
    <thead {...props}>
      {children}
    </thead>
  ),
  th: ({ children, ...props }: any) => (
    <th {...props}>
      {children}
    </th>
  ),
  tbody: ({ children, ...props }: any) => (
    <tbody {...props}>
      {children}
    </tbody>
  ),
  tr: ({ children, ...props }: any) => (
    <tr {...props}>
      {children}
    </tr>
  ),
  td: ({ children, ...props }: any) => (
    <td {...props}>
      {children}
    </td>
  ),
  strong: ({ children, ...props }: any) => (
    <strong {...props}>
      {children}
    </strong>
  ),
  em: ({ children, ...props }: any) => (
    <em {...props}>
      {children}
    </em>
  ),
  del: ({ children, ...props }: any) => (
    <del {...props}>
      {children}
    </del>
  ),
  hr: ({ ...props }: any) => (
    <hr {...props} />
  ),
  a: ({ children, href, ...props }: any) => (
    <a {...props} href={href}>
      {children}
    </a>
  ),
  img: (props: any) => (
    <img {...props} />
  ),
  input: ({ type, checked, ...props }: any) => {
    if (type === 'checkbox') {
      return (
        <input
          type="checkbox"
          checked={checked}
          disabled
          {...props}
        />
      );
    }
    return <input type={type} {...props} />;
  },
  sup: ({ children, ...props }: any) => (
    <sup {...props}>
      {children}
    </sup>
  ),
  sub: ({ children, ...props }: any) => (
    <sub {...props}>
      {children}
    </sub>
  ),
  details: ({ children, ...props }: any) => (
    <details {...props}>
      {children}
    </details>
  ),
  summary: ({ children, ...props }: any) => (
    <summary {...props}>
      {children}
    </summary>
  ),
  dl: ({ children, ...props }: any) => (
    <dl {...props}>
      {children}
    </dl>
  ),
  dt: ({ children, ...props }: any) => (
    <dt {...props}>
      {children}
    </dt>
  ),
  dd: ({ children, ...props }: any) => (
    <dd {...props}>
      {children}
    </dd>
  ),
  abbr: ({ children, ...props }: any) => (
    <abbr {...props}>
      {children}
    </abbr>
  ),
  mark: ({ children, ...props }: any) => (
    <mark {...props}>
      {children}
    </mark>
  ),
  kbd: ({ children, ...props }: any) => (
    <kbd {...props}>
      {children}
    </kbd>
  ),
});

// Memoize the components object to prevent recreation
const MARKDOWN_COMPONENTS = createMarkdownComponents();

// Ensure math is parsed/rendered before other transformations
const MD_REMARK_PLUGINS = [
  remarkGfm,
  remarkBreaks,
  remarkMath,
  remarkEmoji,
  remarkDirective,
  remarkFrontmatter,
  remarkWikiLink,
];
const REHYPE_PLUGINS = [
  [rehypeKatex, { strict: false }],
  rehypeRaw,
  rehypeUnwrapImages,
  rehypeSlug,
  [rehypeAutolinkHeadings, { behavior: 'wrap' }],
  [rehypeSanitize, { allowDangerousHtml: true }]
];


/* ----------  NORMALISE MARKDOWN UTILS  ---------- */
function normalizeMd(md: string): string {
  if (!md) return "";
  
  return dedent(md)
    .replace(/\r\n?/g, "\n") // Normalize line endings
    .trim();
}

// Standard markdown components (minimal styling, default HTML tags)
const STANDARD_MARKDOWN_COMPONENTS = {
  code: (props: any) => <code {...props} />,
  pre: (props: any) => <pre {...props} />,
  h1: (props: any) => <h1 {...props} />,
  h2: (props: any) => <h2 {...props} />,
  h3: (props: any) => <h3 {...props} />,
  h4: (props: any) => <h4 {...props} />,
  h5: (props: any) => <h5 {...props} />,
  h6: (props: any) => <h6 {...props} />,
  p: (props: any) => <p {...props} />,
  ul: (props: any) => <ul {...props} />,
  ol: (props: any) => <ol {...props} />,
  li: (props: any) => <li {...props} />,
  blockquote: (props: any) => <blockquote {...props} />,
  table: (props: any) => <table {...props} />,
  thead: (props: any) => <thead {...props} />,
  th: (props: any) => <th {...props} />,
  tbody: (props: any) => <tbody {...props} />,
  tr: (props: any) => <tr {...props} />,
  td: (props: any) => <td {...props} />,
  strong: (props: any) => <strong {...props} />,
  em: (props: any) => <em {...props} />,
  del: (props: any) => <del {...props} />,
  hr: (props: any) => <hr {...props} />,
  a: (props: any) => <a {...props} />,
  input: (props: any) => <input {...props} disabled />,
  sup: (props: any) => <sup {...props} />,
  sub: (props: any) => <sub {...props} />,
  details: (props: any) => <details {...props} />,
  summary: (props: any) => <summary {...props} />,
  dl: (props: any) => <dl {...props} />,
  dt: (props: any) => <dt {...props} />,
  dd: (props: any) => <dd {...props} />,
  abbr: (props: any) => <abbr {...props} />,
  mark: (props: any) => <mark {...props} />,
  kbd: (props: any) => <kbd {...props} />,
};

// Only use GFM plugins for standard mode
const STANDARD_REMARK_PLUGINS = [remarkGfm, remarkBreaks];
const STANDARD_REHYPE_PLUGINS: unknown[] = [];

// Add a prop to toggle between 'standard' and 'custom' rendering
const SafeMarkdown = memo(({ content, standardMode = false }: { content: string; standardMode?: boolean }) => {
  // Dedent markdown to avoid accidental code blocks from indentation
  const formattedContent = useMemo(() => normalizeMd(content ?? ""), [content]);

  if (formattedContent.trim() === "") return null;

  // For very short content without markdown syntax, render as plain text
  if (
    formattedContent.trim().length < 10 &&
    !formattedContent.includes("`") &&
    !formattedContent.includes("*") &&
    !formattedContent.includes("#") &&
    !formattedContent.includes("-") &&
    !formattedContent.includes("_") &&
    !formattedContent.includes("~") &&
    !formattedContent.includes("[") &&
    !formattedContent.includes("!") &&
    !formattedContent.includes("=")
  ) {
    return (
      <span className="text-foreground text-base">{formattedContent}</span>
    );
  }

  try {
    if (standardMode) {
      // Standard mode: minimal plugins, default tags, no custom styling
      return (
        <div className="markdown-content-standard">
          <ReactMarkdown
            remarkPlugins={STANDARD_REMARK_PLUGINS}
            rehypePlugins={STANDARD_REHYPE_PLUGINS}
            skipHtml={false}
            components={STANDARD_MARKDOWN_COMPONENTS}
            unwrapDisallowed={false}
          >
            {formattedContent}
          </ReactMarkdown>
        </div>
      );
    }
    // Custom mode (default): all plugins, custom components
    return (
      <div className="markdown-content">
        <ReactMarkdown
          remarkPlugins={MD_REMARK_PLUGINS}
          rehypePlugins={REHYPE_PLUGINS}
          skipHtml={false}
          components={MARKDOWN_COMPONENTS}
          unwrapDisallowed={false}
        >
          {formattedContent}
        </ReactMarkdown>
      </div>
    );
  } catch (error) {
    console.warn("Full markdown parsing failed, using basic renderer that preserves newlines:", error, formattedContent);
    return (
      <div className="markdown-content">
        <ReactMarkdown
          remarkPlugins={[remarkGfm, remarkBreaks, remarkEmoji]}
          rehypePlugins={[[rehypeAutolinkHeadings, { behavior: "wrap" }]]}
          skipHtml={true}
          components={MARKDOWN_COMPONENTS}
          unwrapDisallowed={false}
        >
          {formattedContent}
        </ReactMarkdown>
      </div>
    );
  }
});
SafeMarkdown.displayName = "SafeMarkdown";

const ToolGroupBadge = memo(({ group }: { group: { name: string; count: number; calls: ToolCall[] } }) => {
  const [isOpen, setIsOpen] = useState(false);

  const toggleOpen = useCallback(() => {
    setIsOpen(prev => !prev);
  }, []);

  const formatToolResult = (toolName: string, result: string) => {
    let parsed: any;
    
    try {
      parsed = JSON.parse(result);
    } catch {
      // If parsing fails, treat as plain text
      parsed = result;
    }

    // Handle MCP tool results that come as objects with content property
    if (parsed && typeof parsed === 'object' && parsed.content !== undefined) {
      parsed = parsed.content;
    }

    // Handle arrays of content objects (some MCP tools return this format)
    if (Array.isArray(parsed) && parsed.length > 0 && parsed[0].content !== undefined) {
      parsed = parsed.map(item => item.content || item).join('\n');
    }

    // Ensure we have a string to work with
    const displayContent = typeof parsed === 'string' ? parsed : JSON.stringify(parsed, null, 2);
    
    switch (toolName) {
      case 'web_search':
      case 'deep_search': {
        const isDeepSearch = toolName === 'deep_search';
        
        const parseSearchResults = (content: string) => {
          const summaryMatch = content.match(/\n\nSummary: (.*)/s);
          let summary: string | null = null;
          let resultsContent = content;

          if (summaryMatch) {
            summary = summaryMatch[1].trim();
            resultsContent = content.substring(0, summaryMatch.index);
          }
          
          const lines = resultsContent.split('\n\n').filter(line => line.trim() && !line.startsWith('Search results for'));
          
          const results = lines.map(line => {
            const match = line.match(/^(.*?): (.*) \((https?:\/\/[^)]+)\)$/);
            if (match) {
              return { title: match[1], content: match[2], url: match[3] };
            }
            return null; // or some fallback for malformed lines
          }).filter(Boolean) as { title: string; content: string; url: string }[];
          
          return { results, summary };
        };

        if (isDeepSearch) {
          const parts = displayContent.split(/===\s*Results for "([^"]+)"\s*===/);
          const searchTitleMatch = displayContent.match(/^Deep search results for "([^"]+)"/);
          const searchTitle = searchTitleMatch ? searchTitleMatch[1] : "Deep Search";

          const groupedResults = [];
          for (let i = 1; i < parts.length; i += 2) {
              const query = parts[i];
              const content = parts[i + 1];
              if (content) {
                  groupedResults.push({
                      query: query,
                      ...parseSearchResults(content)
                  });
              }
          }
          
        return (
            <div className="space-y-4 max-h-[40rem] overflow-y-auto">
              <div className="font-medium text-base text-blue-600 dark:text-blue-400">Deep Search Results: {searchTitle}</div>
              {groupedResults.map((group, groupIdx) => (
                <div key={groupIdx} className="space-y-3 p-3 bg-muted/20 rounded-lg">
                  <h3 className="font-semibold text-base text-blue-500 dark:text-blue-300">Results for: "{group.query}"</h3>
                  {group.results.map((result, idx) => (
                    <div key={idx} className="p-3 bg-background/50 rounded-md border border-border/30">
                      <a href={result.url} target="_blank" rel="noopener noreferrer" className="text-sm font-medium text-primary hover:underline line-clamp-1">{result.title}</a>
                      <p className="text-sm text-muted-foreground mt-1 line-clamp-2">{result.content}</p>
                </div>
              ))}
                  {group.summary && (
                    <div className="mt-2 p-2 bg-blue-50/20 dark:bg-blue-900/10 rounded">
                      <p className="text-sm italic text-blue-800 dark:text-blue-200"><span className="font-semibold">Summary:</span> {group.summary}</p>
            </div>
                  )}
                </div>
              ))}
          </div>
        );
        }

        // Standard web search
        const { results, summary } = parseSearchResults(displayContent);
        const queryMatch = displayContent.match(/^Search results for "([^"]+)"/);
        const query = queryMatch ? queryMatch[1] : 'Web Search';

        return (
          <div className="space-y-3 max-h-96 overflow-y-auto">
            <div className="font-medium text-base text-blue-600 dark:text-blue-400">Search Results: {query}</div>
            <div className="space-y-2">
              {results.map((result, idx) => (
                <div key={idx} className="p-3 bg-muted/30 rounded-lg border border-border/30">
                   <a href={result.url} target="_blank" rel="noopener noreferrer" className="text-sm font-medium text-primary hover:underline flex items-center gap-1">
                     <span>{result.title}</span> <Link size={12} />
                   </a>
                   <p className="text-sm text-muted-foreground mt-1">{result.content}</p>
                </div>
              ))}
            </div>
            {summary && (
              <div className="mt-3 p-3 bg-blue-100/30 dark:bg-blue-900/20 rounded-lg border border-blue-200/50 dark:border-blue-800/50">
                <p className="text-sm text-blue-800 dark:text-blue-200"><strong className="font-semibold">Summary:</strong> {summary}</p>
              </div>
            )}
          </div>
        );
      }
      
      case 'weather': {
        const weatherRegex = /Weather for ([^:]+):\s*Current: ([\d.-]+)°C, ([^\n]+)\s*Feels like: ([\d.-]+)°C\s*Humidity: ([\d]+)%\s*Wind: ([^ \n]+) km\/h\s*Pressure: ([\d]+) hPa\s*Visibility: ([\d.]+) km/;
        const simulatedWeatherRegex = /Weather for (.+): This is simulated weather data. .*Current: ([\d.-]+)°C, ([^,]+), Humidity: ([\d]+)%, Wind: (.+)/;
        
        const match = displayContent.match(weatherRegex);
        const simulatedMatch = displayContent.match(simulatedWeatherRegex);

        const getWeatherIcon = (description: string) => {
          const desc = description.toLowerCase();
          if (desc.includes('rain') || desc.includes('drizzle')) return <CloudRain size={24} className="text-blue-400" />;
          if (desc.includes('cloud')) return <Cloudy size={24} className="text-gray-400" />;
          if (desc.includes('sun') || desc.includes('clear')) return <Sun size={24} className="text-yellow-400" />;
          return <Cloudy size={24} className="text-gray-400" />;
        };

        if (match) {
          const [, location, temp, description, feelsLike, humidity, wind, pressure, visibility] = match;
          const weatherData = { location, temp, description, feelsLike, humidity, wind, pressure, visibility };
          
          return (
            <div className="space-y-3">
              <div className="font-medium text-base text-blue-500 dark:text-blue-400">Weather Report: {weatherData.location}</div>
              <div className="p-4 bg-blue-50/30 dark:bg-blue-900/10 rounded-lg border border-blue-200/30 dark:border-blue-800/30">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    {getWeatherIcon(weatherData.description)}
                    <div>
                      <div className="text-3xl font-bold">{weatherData.temp}°C</div>
                      <div className="text-sm text-muted-foreground capitalize">{weatherData.description}</div>
                    </div>
                  </div>
                  <div className="text-right text-xs text-muted-foreground">
                    Feels like {weatherData.feelsLike}°C
                  </div>
                </div>
                <div className="mt-4 grid grid-cols-2 gap-3 text-sm pt-3 border-t border-blue-200/50 dark:border-blue-800/50">
                  <div className="flex items-center gap-2"><Droplets size={14} /> Humidity: {weatherData.humidity}%</div>
                  <div className="flex items-center gap-2"><Wind size={14} /> Wind: {weatherData.wind} km/h</div>
                  <div className="flex items-center gap-2"><Gauge size={14} /> Pressure: {weatherData.pressure} hPa</div>
                  <div className="flex items-center gap-2"><Eye size={14} /> Visibility: {weatherData.visibility} km</div>
                </div>
              </div>
            </div>
          );
        }

        if (simulatedMatch) {
            const [, location, temp, description, humidity, wind] = simulatedMatch;
            return (
              <div className="space-y-2">
                <div className="font-medium text-base text-blue-500 dark:text-blue-400">Weather Report: {location} (Simulated)</div>
                <div className="text-sm bg-yellow-50/50 dark:bg-yellow-900/20 p-2 rounded-md border border-yellow-200/50 dark:border-yellow-800/50 text-yellow-700 dark:text-yellow-300">
                  This is simulated data. Add your OpenWeatherMap API key for live weather.
                </div>
                <div className="p-3 bg-blue-50/30 dark:bg-blue-900/10 rounded-lg border border-blue-200/30 dark:border-blue-800/30 text-sm">
                  <div className="flex items-center gap-2"><Thermometer size={14} /> Temp: {temp}°C, {description}</div>
                  <div className="flex items-center gap-2 mt-2"><Droplets size={14} /> Humidity: {humidity}%</div>
                  <div className="flex items-center gap-2 mt-2"><Wind size={14} /> Wind: {wind}</div>
                </div>
              </div>
            );
        }
        
        // Fallback for errors or un-parsable format
        return (
          <div className="space-y-2">
            <div className="font-medium text-base text-blue-600 dark:text-blue-400">Weather Report:</div>
            <div className="text-sm bg-blue-50/30 dark:bg-blue-900/10 p-3 rounded border border-blue-200/30 dark:border-blue-800/30">
              <pre className="whitespace-pre-wrap text-sm leading-relaxed">{displayContent}</pre>
            </div>
          </div>
        );
      }
      
      case 'calculator':
        return (
          <div className="space-y-2">
            <div className="font-medium text-base text-green-600 dark:text-green-400">Calculation Result:</div>
            <div className="text-lg font-mono bg-green-50/30 dark:bg-green-900/10 p-3 rounded border border-green-200/30 dark:border-green-800/30 text-center">
              {displayContent}
            </div>
          </div>
        );
      
      case 'datetime':
        return (
          <div className="space-y-2">
            <div className="font-medium text-base text-purple-600 dark:text-purple-400">Date & Time:</div>
            <div className="text-sm bg-purple-50/30 dark:bg-purple-900/10 p-3 rounded border border-purple-200/30 dark:border-purple-800/30">
              {displayContent}
            </div>
          </div>
        );
      
      case 'research': {
        const completionMatch = displayContent.match(/Research completed\. \(credits used: (\d+)\)\n\n([\s\S]*)/);
        const timeoutMatch = displayContent.match(/Research timed out after (\d+)s\. Task ID: (.+)/);
        const errorMatch = displayContent.match(/Research (?:task failed|error): (.+)/);
        
        if (timeoutMatch) {
          const [, seconds, taskId] = timeoutMatch;
          return (
            <div className="space-y-3">
              <div className="font-medium text-base text-amber-600 dark:text-amber-400">Research Report</div>
              <div className="p-4 bg-yellow-50/30 dark:bg-yellow-900/10 rounded-lg border border-yellow-200/30 dark:border-yellow-800/30">
                <div className="flex items-center gap-2 mb-2">
                  <Clock size={16} className="text-yellow-600" />
                  <span className="font-semibold text-sm text-yellow-800 dark:text-yellow-200">Research Timeout</span>
                </div>
                <p className="text-sm text-muted-foreground">
                  Research task timed out after {seconds} seconds. Task ID: {taskId}
                </p>
                <p className="text-sm text-muted-foreground mt-2">
                  The research may have completed on the server. Please try again.
                </p>
              </div>
            </div>
          );
        }
        
        if (errorMatch) {
          const [, errorMsg] = errorMatch;
          return (
            <div className="space-y-3">
              <div className="font-medium text-base text-amber-600 dark:text-amber-400">Research Report</div>
              <div className="p-4 bg-red-50/30 dark:bg-red-900/10 rounded-lg border border-red-200/30 dark:border-red-800/30">
                <div className="flex items-center gap-2 mb-2">
                  <X size={16} className="text-red-600" />
                  <span className="font-semibold text-sm text-red-800 dark:text-red-200">Research Failed</span>
                </div>
                <p className="text-sm text-muted-foreground">{errorMsg}</p>
              </div>
            </div>
          );
        }
        
        if (completionMatch) {
          const [, credits, researchData] = completionMatch;
          
          // Try to parse as JSON first
          let parsedData;
          try {
            parsedData = JSON.parse(researchData);
          } catch {
            parsedData = researchData;
          }
          
          return (
            <div className="space-y-3 max-h-[40rem] overflow-y-auto">
              <div className="flex items-center justify-between">
                <div className="font-medium text-base text-amber-600 dark:text-amber-400">Research Report</div>
                <div className="text-xs text-muted-foreground px-2 py-1 bg-muted/50 rounded-full">
                  {credits} credits used
                </div>
              </div>
              <div className="p-4 bg-amber-50/30 dark:bg-amber-900/10 rounded-lg border border-amber-200/30 dark:border-amber-800/30">
                {typeof parsedData === 'string' ? (
                  <MemoizedMarkdown content={parsedData} />
                ) : (
                  <div className="text-sm leading-relaxed">
                    <pre className="whitespace-pre-wrap text-sm bg-background/30 p-3 rounded border overflow-x-auto">
                      {JSON.stringify(parsedData, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            </div>
          );
        }
        
        // Fallback for any other research content
        return (
          <div className="space-y-3">
            <div className="font-medium text-base text-amber-600 dark:text-amber-400">Research Report</div>
            <div className="p-4 bg-amber-50/30 dark:bg-amber-900/10 rounded-lg border border-amber-200/30 dark:border-amber-800/30">
              <MemoizedMarkdown content={displayContent} />
            </div>
          </div>
        );
      }
      
      case 'thinking': {
        return (
          <div className="space-y-3">
            <div className="font-medium text-base text-orange-600 dark:text-orange-400">Thought Process</div>
            <div className="p-4 bg-orange-50/30 dark:bg-orange-900/10 rounded-lg border border-orange-200/30 dark:border-orange-800/30">
              <MemoizedMarkdown content={displayContent} />
            </div>
          </div>
        );
      }
      
      case 'url_fetch':
        return (
          <div className="space-y-2 max-h-80 overflow-y-auto">
            <div className="font-medium text-base text-indigo-600 dark:text-indigo-400">Fetched Content:</div>
            <div className="text-sm leading-relaxed bg-indigo-50/30 dark:bg-indigo-900/10 p-3 rounded border border-indigo-200/30 dark:border-indigo-800/30">
              <div className="whitespace-pre-wrap break-words">{displayContent}</div>
            </div>
          </div>
        );
      
      case 'code_analysis':
        return (
          <div className="space-y-2 max-h-80 overflow-y-auto">
            <div className="font-medium text-base text-emerald-600 dark:text-emerald-400">Code Analysis:</div>
            <div className="text-sm leading-relaxed bg-emerald-50/30 dark:bg-emerald-900/10 p-3 rounded border border-emerald-200/30 dark:border-emerald-800/30">
              <MemoizedMarkdown content={displayContent} />
            </div>
          </div>
        );
      
      case 'memory': {
        const storeMatch = displayContent.match(/Stored information for future reference - "([^"]+)": (.*)/);
        const retrieveMatch = displayContent.match(/Retrieved stored information for "([^"]+)": (.*)/);
        const notFoundMatch = displayContent.match(/No stored information found for "([^"]+)"/);

        let icon, title, content;

        if (storeMatch) {
          icon = <Database size={16} className="text-violet-500" />;
          title = "Stored in Memory";
          content = <><span className="font-semibold">{storeMatch[1]}:</span> {storeMatch[2]}</>;
        } else if (retrieveMatch) {
          icon = <Search size={16} className="text-violet-500" />;
          title = "Retrieved from Memory";
          content = <><span className="font-semibold">{retrieveMatch[1]}:</span> {retrieveMatch[2]}</>;
        } else if (notFoundMatch) {
          icon = <FileText size={16} className="text-violet-500" />;
          title = "Memory Lookup";
          content = <>No information found for: <span className="font-semibold">{notFoundMatch[1]}</span></>;
        } else {
        return (
          <div className="space-y-2">
            <div className="font-medium text-base text-violet-600 dark:text-violet-400">Memory Operation:</div>
            <div className="text-sm bg-violet-50/30 dark:bg-violet-900/10 p-3 rounded border border-violet-200/30 dark:border-violet-800/30">
              {displayContent}
            </div>
          </div>
        );
        }

        return (
          <div className="space-y-2">
             <div className="font-medium text-base text-violet-600 dark:text-violet-400">{title}</div>
             <div className="flex items-start gap-3 text-sm bg-violet-50/30 dark:bg-violet-900/10 p-3 rounded-lg border border-violet-200/30 dark:border-violet-800/30">
               <div className="flex-shrink-0 mt-0.5">{icon}</div>
               <div className="flex-1">{content}</div>
             </div>
           </div>
        );
      }
      
      default:
        // For MCP tools and other unknown tools, handle content gracefully
        return (
          <div className="max-h-64 overflow-y-auto">
            <div className="text-sm leading-relaxed bg-muted/30 p-3 rounded">
              <div className="whitespace-pre-wrap break-words">{displayContent}</div>
            </div>
          </div>
        );
    }
  };

  const getToolIcon = (toolName: string) => {
    switch (toolName) {
      case 'web_search':
        return <Search size={12} className="text-blue-500" />;
       case 'deep_search':
         return <Zap size={12} className="text-blue-600" />;
       case 'research':
         return <BookOpen size={12} className="text-amber-600" />;
      case 'weather':
        return <CloudRain size={12} className="text-blue-400" />;
      case 'calculator':
        return <Calculator size={12} className="text-green-500" />;
      case 'datetime':
        return <Clock size={12} className="text-purple-500" />;
      case 'thinking':
        return <Brain size={12} className="text-orange-500" />;
      case 'memory':
        return <FileText size={12} className="text-indigo-500" />;
      case 'url_fetch':
        return <Link size={12} className="text-teal-500" />;
      case 'code_analysis':
        return <Code size={12} className="text-red-500" />;
      default:
        return <Wrench size={12} className="text-primary" />;
    }
  };

  const getToolDisplayName = (toolName: string) => {
    return toolName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const hasResults = group.calls.some(call => call.result);

  // Modern unified badge design matching InlineToolBadge style
  const config = getToolConfig(group.name);
  const { label } = getToolDetails(group.name, group.calls[0]?.arguments || '{}');
  
  return (
    <div className={cn(
      "inline-flex items-center transition-all duration-200 cursor-pointer",
      isOpen ? "flex-col items-stretch w-full" : "max-w-fit"
    )}>
      <button
        onClick={toggleOpen}
        className={cn(
          "inline-flex items-center gap-1.5 px-2 py-0.5 text-xs font-medium transition-all duration-200",
          "hover:shadow-lg backdrop-blur-sm cursor-pointer border-0 rounded-md",
          getToolBg(group.name),
          !isOpen && "rounded-md",
          isOpen && "rounded-t-md rounded-b-none w-full justify-between"
        )}
      >
        <div className="flex items-center gap-1.5">
          <div className="flex items-center justify-center w-3 h-3 rounded-sm">
            {getToolIcon(group.name)}
          </div>
          <span className="text-foreground/90 font-semibold tracking-tight">
            {label}
            {group.count > 1 && (
              <span className="ml-1 text-[10px] px-1 py-0.5 bg-foreground/10 text-foreground/80 rounded font-bold">
                {group.count}
              </span>
            )}
          </span>
        </div>
        
        {hasResults && (
          <ChevronDown 
            size={10} 
            className={cn(
              "text-foreground/60 transition-transform duration-200",
              isOpen && "rotate-180"
            )} 
          />
        )}
      </button>
      {isOpen && hasResults && (
  <div className={cn(
    "border-0 rounded-b-md shadow-lg animate-in slide-in-from-top-2 duration-200",
    getToolBg(group.name)
  )}>
   <div className="p-3">
     {group.count === 1 ? (
       // Single result - full width
       (group.calls.map((call, idx) => (
         call.result && (
           <div key={idx} className="p-3 bg-muted/30 rounded-lg overflow-hidden">
             {formatToolResult(call.name, call.result)}
           </div>
         )
       )))
     ) : (
       // Multiple results - side by side or stacked based on content type
       (<div className={`${group.name === 'web_search' || group.name === 'deep_search' ? 'space-y-3' : 'grid grid-cols-1 lg:grid-cols-2 gap-3'}`}>
         {group.calls.map((call, idx) => (
           call.result && (
             <div key={idx} className="p-3 bg-muted/30 rounded-lg overflow-hidden">
               <div className="text-sm text-muted-foreground mb-2 font-medium">
                 {group.name === 'web_search' || group.name === 'deep_search' 
                   ? `Search ${idx + 1}:` 
                   : `Result ${idx + 1}:`
                 }
               </div>
               {formatToolResult(call.name, call.result)}
             </div>
           )
         ))}
       </div>)
     )}
   </div>
 </div>
)}
    </div>
  );
});

ToolGroupBadge.displayName = 'ToolGroupBadge';

// Component for displaying reasoning content
const ReasoningSection = memo(({ message, isStreaming }: { message: MessageBubbleProps['message']; isStreaming?: boolean }) => {
  const thinking = message.thinking;
  const [isOpen, setIsOpen] = useState(false); // Collapsed by default
  const [startTime, setStartTime] = useState<number | null>(null);

  // Debounce thinking content to reduce rerenders during streaming
  const debouncedThinking = useDebounce(thinking, 150);

  // Track when reasoning starts
  useEffect(() => {
    if (isStreaming && thinking && !startTime) {
      setStartTime(Date.now());
    } else if (!isStreaming && startTime) {
      setStartTime(null);
    }
  }, [isStreaming, thinking, startTime]);

  // Use optimized timer for DOM-only updates
  const { timerRef } = useOptimizedTimer({ 
    startTime, 
    isActive: isStreaming && !!startTime 
  });

  const toggleOpen = useCallback(() => {
    setIsOpen(prev => !prev);
  }, []);

  // Format timing duration
  const formatDuration = useCallback((startTime: number, endTime: number) => {
    const diffMs = endTime - startTime;
    const diffSeconds = Math.floor(diffMs / 1000);
    const diffMinutes = Math.floor(diffSeconds / 60);
    
    if (diffMinutes > 0) {
      return `${diffMinutes}m ${diffSeconds % 60}s`;
    } else {
      return `${diffSeconds}s`;
    }
  }, []);

  // Get timing text using actual message timing data
  const getTimingText = useCallback(() => {
    const metrics = message.generationMetrics;
    
    if (isStreaming && startTime) {
      return 'Thinking';
    } else if (!isStreaming && thinking) {
      // Try to get reasoning time from multiple possible sources
      let reasoningMs = metrics?.reasoningTimeMs;
      
      // If no specific reasoning time, estimate from total generation time
      if (!reasoningMs && metrics?.generationTimeMs) {
        reasoningMs = metrics.generationTimeMs;
      }
      
      // If still no timing data, use a default
      if (!reasoningMs) {
        return 'Thought for a moment';
      }
      
      // If less than 10 seconds, show generic text
      if (reasoningMs < 10000) {
        return 'Thought for a few seconds';
      }
      
      const duration = formatDuration(0, reasoningMs);
      return `Thought for ${duration}`;
    }
    return 'Thinking';
  }, [isStreaming, startTime, thinking, formatDuration, message.generationMetrics]);

  // Simple preview for collapsed state with markdown rendering
  const getPreview = useMemo(() => {
    if (!thinking) return null;
    const text = thinking.trim();
    const previewText = text.length <= 80 ? text : text.substring(0, 80) + '...';
    return previewText;
  }, [thinking]);

  if (!thinking) return null;

  return (
    <div className="my-2">
      {/* Collapsed state - simple timing display */}
      {!isOpen ? (
        <button
          onClick={toggleOpen}
          className="group inline-flex items-center gap-1 text-sm text-foreground/80 hover:text-foreground transition-colors cursor-pointer"
        >
          <span className="font-medium">
            {getTimingText()}
            {isStreaming && startTime && <span ref={timerRef}></span>}
          </span>
          <ChevronDown 
            size={14} 
            className="text-foreground/40 group-hover:text-foreground/60 transition-colors ml-1" 
          />
        </button>
      ) : (
        /* Expanded state - clean thinking content */
        <div className="space-y-2">
          <button
            onClick={toggleOpen}
            className="group inline-flex items-center gap-1 text-sm text-foreground/80 hover:text-foreground transition-colors cursor-pointer"
          >
            <span className="font-medium">
              {getTimingText()}
            </span>
            <ChevronDown 
              size={14} 
              className="text-foreground/40 group-hover:text-foreground/60 transition-colors rotate-180 ml-1" 
            />
          </button>
          
          {/* Thinking content with clean, text-like styling */}
          <div className="pl-4 border-l-2 border-muted-foreground/20">
            <div className="prose prose-sm dark:prose-invert max-w-none text-muted-foreground/90">
              <MemoizedMarkdown content={thinking ?? ""} />
            </div>
          </div>
        </div>
      )}
    </div>
  );
});

ReasoningSection.displayName = 'ReasoningSection';

// New component for showing message versions
interface MessageVersionSelectorProps {
  conversationId: Id<"conversations">;
  currentBranchId: string;
  messagePosition: number; // Position of this message in the conversation
  onSwitchBranch: (branchId: string) => void;
}

const MessageVersionSelector = memo(function MessageVersionSelector({ conversationId, currentBranchId, messagePosition, onSwitchBranch }: MessageVersionSelectorProps) {
  const branchStats = useQuery(api.branches.getBranchStats, { conversationId });
  
  const formatTime = useCallback((timestamp: number) => {
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));

    if (minutes < 1) return "now";
    if (minutes < 60) return `${minutes}m`;
    if (hours < 24) return `${hours}h`;
    return new Date(timestamp).toLocaleDateString();
  }, []);

  const handleSwitchBranch = useCallback((branchId: string) => {
    onSwitchBranch(branchId);
  }, [onSwitchBranch]);

  if (!branchStats || branchStats.length <= 1) return null;

  // Find branches that have messages at this position or beyond
  const relevantBranches = branchStats.filter(branch => {
    return branch.messageCount > messagePosition;
  });

  if (relevantBranches.length <= 1) return null;

  const currentBranch = branchStats.find(b => b.branchId === currentBranchId);

  return (
    <div className="flex items-center gap-1 mb-1 pl-3 sm:pl-5 opacity-60 hover:opacity-100 transition-opacity">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="sm" className="h-5 text-[11px] gap-1 px-1.5">
            <GitBranch size={10} />
            <span className="max-w-20 truncate">
              {currentBranch?.title?.replace(/^(Edit|Retry):\s*/, '') || currentBranchId}
            </span>
            <ChevronDown size={8} />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent 
          align="end" 
          avoidCollisions={true}
          collisionPadding={{ top: 0, right: 16, bottom: 16, left: 16 }}
          className="w-[calc(100vw-2rem)] sm:w-auto min-w-[200px] sm:min-w-[240px] max-h-[50vh] sm:max-h-[60vh] overflow-y-auto bg-background border border-border shadow-lg rounded-xl p-2 sm:p-1">
          <div className="px-2 py-1 text-xs font-medium text-muted-foreground border-b">
            Message Versions ({relevantBranches.length})
          </div>
          {relevantBranches.map((branch) => (
            <DropdownMenuItem
              key={branch.branchId}
              onClick={() => handleSwitchBranch(branch.branchId)}
              className={cn(
                "flex items-start justify-between text-xs p-2",
                branch.branchId === currentBranchId && "bg-muted"
              )}
            >
              <div className="flex-1 min-w-0 space-y-1">
                <div className="flex items-center gap-2">
                  <GitBranch size={10} className="text-muted-foreground flex-shrink-0" />
                  <span className="font-medium truncate">
                    {branch.title}
                  </span>
                  {branch.branchId === currentBranchId && (
                    <Badge variant="secondary" className="text-xs px-1 py-0">
                      Current
                    </Badge>
                  )}
                </div>
                
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <span>{branch.messageCount} msgs</span>
                  <span>•</span>
                  <span>{formatTime(branch.lastMessageAt)}</span>
                </div>
                
                {branch.parentBranchId && branch.parentBranchId !== "main" && (
                  <div className="text-xs text-muted-foreground">
                    ↳ from {branchStats.find(b => b.branchId === branch.parentBranchId)?.title?.slice(0, 20) || branch.parentBranchId}
                  </div>
                )}
              </div>
            </DropdownMenuItem>
          ))}
          <DropdownMenuSeparator />
          <div className="px-2 py-1 text-xs text-muted-foreground">
            💡 Different AI responses to compare
          </div>
        </DropdownMenuContent>
      </DropdownMenu>
      <div className="text-[10px] text-muted-foreground">
        {relevantBranches.length} versions
      </div>
    </div>
  );
});

// Component to render file attachments with proper URL handling
function AttachmentRenderer({ attachment, isSharedView }: { attachment: any; isSharedView?: boolean }) {
  const fileData = useQuery(api.files.getFile, 
    attachment.storageId ? { storageId: attachment.storageId } : "skip"
  );

  // State for click-to-load functionality
  const [showAudioPlayer, setShowAudioPlayer] = useState(false);
  const [showVideoPlayer, setShowVideoPlayer] = useState(false);

  // Helper function to get file icon based on type
  const getFileIcon = (name: string, type: string) => {
    if (type.startsWith('image/')) return '🖼️';
    if (type.startsWith('audio/')) return '🎵';
    if (type.startsWith('video/')) return '🎬';
    if (name.toLowerCase().endsWith('.pdf') || type === 'application/pdf') return '📄';
    if (name.toLowerCase().endsWith('.txt') || type === 'text/plain') return '📝';
    if (name.toLowerCase().endsWith('.csv') || type === 'text/csv') return '📊';
    if (name.toLowerCase().endsWith('.json') || type === 'application/json') return '📋';
    if (name.toLowerCase().endsWith('.md')) return '📖';
    if (name.toLowerCase().endsWith('.docx') || type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') return '📃';
    if (name.toLowerCase().endsWith('.doc') || type === 'application/msword') return '📑';
    if (name.toLowerCase().endsWith('.rtf') || type === 'application/rtf' || type === 'text/rtf') return '📜';
    return '📎';
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return '';
    const sizes = ['B', 'KB', 'MB'];
    let size = bytes;
    let i = 0;
    while (size >= 1024 && i < 2) {
      size /= 1024;
      i++;
    }
    return ` (${size.toFixed(1)} ${sizes[i]})`;
  };

  const getFileTypeDisplay = (name: string, type: string) => {
    if (type.startsWith('image/')) return 'Image';
    if (type.startsWith('audio/')) return 'Audio';
    if (type.startsWith('video/')) return 'Video';
    if (name.toLowerCase().endsWith('.pdf') || type === 'application/pdf') return 'PDF Document';
    if (name.toLowerCase().endsWith('.docx') || type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') return 'Word Document';
    if (name.toLowerCase().endsWith('.doc') || type === 'application/msword') return 'Word Document';
    if (name.toLowerCase().endsWith('.rtf') || type === 'application/rtf' || type === 'text/rtf') return 'Rich Text Document';
    if (name.toLowerCase().endsWith('.txt') || type === 'text/plain') return 'Text Document';
    if (name.toLowerCase().endsWith('.csv') || type === 'text/csv') return 'CSV Document';
    if (name.toLowerCase().endsWith('.json') || type === 'application/json') return 'JSON Document';
    if (name.toLowerCase().endsWith('.md')) return 'Markdown Document';
    return 'File';
  };

  if (attachment.type === "image") {
    // If we have a storageId, use the file URL from the query
    if (attachment.storageId && fileData?.url) {
      return (
        <div className="space-y-1">
          <LazyImage 
            src={fileData.url} 
            alt={attachment.name || "Image"} 
            className="max-w-[200px] rounded-lg border border-border/20 cursor-pointer hover:border-border/40 transition-all duration-200" 
            onClick={() => !isSharedView && fileData.url && window.open(fileData.url, '_blank')}
          />
          <div className="flex items-center gap-1.5 text-xs text-muted-foreground/60">
            <span className="px-1.5 py-0.5 bg-muted/20 rounded text-xs font-medium">
              {getFileTypeDisplay(attachment.name || '', attachment.type || '')}
            </span>
            {attachment.size && (
              <span className="opacity-50 text-xs">{formatFileSize(attachment.size)}</span>
            )}
          </div>
        </div>
      );
    }
    // Fallback to the blob URL for immediate display (if available)
    else if (attachment.url) {
      return (
        <div className="space-y-1">
          <LazyImage 
            src={attachment.url} 
            alt={attachment.name || "Image"} 
            className="max-w-[200px] rounded-lg border border-border/20 cursor-pointer hover:border-border/40 transition-all duration-200" 
          />
          <div className="flex items-center gap-1.5 text-xs text-muted-foreground/60">
            <span className="px-1.5 py-0.5 bg-muted/20 rounded text-xs font-medium">
              {getFileTypeDisplay(attachment.name || '', attachment.type || '')}
            </span>
            {attachment.size && (
              <span className="opacity-50 text-xs">{formatFileSize(attachment.size)}</span>
            )}
          </div>
        </div>
      );
    }
    // Show clean placeholder if no URL available
    else {
      return (
        <div className="flex items-center gap-2 p-2 bg-muted/15 rounded-lg border border-border/20">
          <div className="w-8 h-8 bg-muted/30 rounded flex items-center justify-center">
            <span className="text-sm">{getFileIcon(attachment.name || '', 'image/')}</span>
          </div>
          <div className="flex-1 min-w-0">
            <div className="text-xs font-medium">{getFileTypeDisplay(attachment.name || '', 'image/')}</div>
            <div className="text-xs text-muted-foreground/60 opacity-70">
              {attachment.size ? formatFileSize(attachment.size) : 'Image file'}
            </div>
          </div>
        </div>
      );
    }
  }

  // Handle audio files with click-to-load
  if (attachment.type === "audio") {
    if (attachment.storageId && fileData?.url) {
      if (!showAudioPlayer) {
        return (
          <AttachmentPlaceholder 
            attachment={attachment}
            onClick={!isSharedView ? () => setShowAudioPlayer(true) : undefined}
          />
        );
      }
      return (
        <div className="space-y-2">
          <div className="border border-border/30 rounded-lg p-4 bg-muted/20">
            <div className="flex items-center gap-3 mb-3">
              <span className="text-2xl">🎵</span>
              <div className="flex-1 min-w-0">
                <div className="text-sm font-medium truncate">{attachment.name || "Audio"}</div>
                <div className="text-sm text-muted-foreground">
                  Audio file{formatFileSize(attachment.size)}
                </div>
              </div>
            </div>
            <audio 
              controls 
              className="w-full"
              src={fileData.url}
            >
              Your browser does not support the audio element.
            </audio>
          </div>
        </div>
      );
    } else if (attachment.url) {
      if (!showAudioPlayer) {
        return (
          <AttachmentPlaceholder 
            attachment={attachment}
            onClick={!isSharedView ? () => setShowAudioPlayer(true) : undefined}
          />
        );
      }
      return (
        <div className="space-y-2">
          <div className="border border-border/30 rounded-lg p-4 bg-muted/20">
            <div className="flex items-center gap-3 mb-3">
              <span className="text-2xl">🎵</span>
              <div className="flex-1 min-w-0">
                <div className="text-sm font-medium truncate">{attachment.name || "Audio"}</div>
                <div className="text-sm text-muted-foreground">
                  Audio file{formatFileSize(attachment.size)}
                </div>
              </div>
            </div>
            <audio 
              controls 
              className="w-full"
              src={attachment.url}
            >
              Your browser does not support the audio element.
            </audio>
          </div>
        </div>
      );
    } else {
      return (
        <AttachmentPlaceholder 
          attachment={attachment}
          onClick={!isSharedView ? () => setShowAudioPlayer(true) : undefined}
        />
      );
    }
  }

  // Handle video files with click-to-load
  if (attachment.type === "video") {
    if (attachment.storageId && fileData?.url) {
      if (!showVideoPlayer) {
        return (
          <AttachmentPlaceholder 
            attachment={attachment}
            onClick={!isSharedView ? () => setShowVideoPlayer(true) : undefined}
          />
        );
      }
      return (
        <div className="space-y-2">
          <div className="border border-border/30 rounded-lg overflow-hidden bg-muted/20">
            <div className="flex items-center gap-3 p-3 border-b border-border/30">
              <span className="text-xl">🎬</span>
              <div className="flex-1 min-w-0">
                <div className="text-sm font-medium truncate">{attachment.name || "Video"}</div>
                <div className="text-sm text-muted-foreground">
                  Video file{formatFileSize(attachment.size)}
                </div>
              </div>
            </div>
            <video 
              controls 
              className="w-full max-h-96"
              src={fileData.url}
              preload="metadata"
            >
              Your browser does not support the video element.
            </video>
          </div>
        </div>
      );
    } else if (attachment.url) {
      if (!showVideoPlayer) {
        return (
          <AttachmentPlaceholder 
            attachment={attachment}
            onClick={!isSharedView ? () => setShowVideoPlayer(true) : undefined}
          />
        );
      }
      return (
        <div className="space-y-2">
          <div className="border border-border/30 rounded-lg overflow-hidden bg-muted/20">
            <div className="flex items-center gap-3 p-3 border-b border-border/30">
              <span className="text-xl">🎬</span>
              <div className="flex-1 min-w-0">
                <div className="text-sm font-medium truncate">{attachment.name || "Video"}</div>
                <div className="text-sm text-muted-foreground">
                  Video file{formatFileSize(attachment.size)}
                </div>
              </div>
            </div>
            <video 
              controls 
              className="w-full max-h-96"
              src={attachment.url}
              preload="metadata"
            >
              Your browser does not support the video element.
            </video>
          </div>
        </div>
      );
    } else {
      return (
        <AttachmentPlaceholder 
          attachment={attachment}
          onClick={!isSharedView ? () => setShowVideoPlayer(true) : undefined}
        />
      );
    }
  }

  // For non-media files, use click-to-load pattern
  if (attachment.storageId && fileData?.url) {
    const icon = getFileIcon(attachment.name || '', fileData.fileType || '');
    const isPDF = attachment.name?.toLowerCase().endsWith('.pdf') || fileData.fileType === 'application/pdf';
    const fileTypeDisplay = getFileTypeDisplay(attachment.name || '', fileData.fileType || '');
    
    return (
      <div className="space-y-2">
        <AttachmentPlaceholder 
          attachment={attachment}
          onClick={!isSharedView ? () => fileData.url && window.open(fileData.url, '_blank') : undefined}
        />
        
        {/* Show extracted text preview if available */}
        {attachment.extractedText && attachment.extractedText.length > 50 && (
          <Collapsible className="w-full">
            <CollapsibleTrigger className="w-full">
              <div className="text-sm flex items-center justify-between px-2 py-1 cursor-pointer text-muted-foreground hover:text-foreground">
                <span>View extracted text ({attachment.extractedText.length} characters)</span>
                <ChevronDown size={14} className="text-muted-foreground" />
            </div>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <div className="mt-2 p-3 bg-muted/20 rounded border text-muted-foreground text-sm whitespace-pre-wrap max-h-64 overflow-y-auto">
                {attachment.extractedText}
              </div>
            </CollapsibleContent>
          </Collapsible>
        )}
      </div>
    );
  }

  // Fallback for files without proper URLs
  const icon = getFileIcon(attachment.name || '', attachment.mimeType || attachment.contentType || '');
  const fileTypeDisplay = getFileTypeDisplay(attachment.name || '', attachment.mimeType || attachment.contentType || '');
  
  return (
    <div className="flex items-center gap-3 p-4 bg-muted/30 rounded-lg border border-border/30">
      <span className="text-2xl">{icon}</span>
      <div className="flex-1 min-w-0">
        <div className="text-sm font-medium truncate">{attachment.name || "File"}</div>
        <div className="text-sm text-muted-foreground">
          {fileTypeDisplay}{formatFileSize(attachment.size)}
        </div>
        {attachment.extractedText && (
          <div className="text-sm text-muted-foreground mt-1">
            Text content available for AI analysis
          </div>
        )}
      </div>
    </div>
  );
}

/* ----------  MEMOISED MARKDOWN RENDERER  ---------- */
const MemoizedMarkdown = memo(({ content }: { content: string }) => {
  if (!content?.trim()) return null;
  return (
    <ReactMarkdown
      remarkPlugins={MD_REMARK_PLUGINS}
      rehypePlugins={REHYPE_PLUGINS}
      skipHtml={false}
      components={MARKDOWN_COMPONENTS}
    >
      {content}
    </ReactMarkdown>
  );
});
MemoizedMarkdown.displayName = "MemoizedMarkdown";

// Shared Canvas data shape
type CanvasData = {
  type: "markdown" | "code" | "chart" | "react";
  title: string;
  content: string;
  language?: string;
  chartSpec?: string;
  library?: "chartjs" | "echarts" | "d3";
  updatedAt: number;
  };
// Modern tool configuration with enhanced styling
const TOOL_CONFIG = {
  web_search: { icon: Search, color: '#3b82f6', bg: 'bg-blue-500/10', label: 'Search' },
  deep_search: { icon: Zap, color: '#6366f1', bg: 'bg-indigo-500/10', label: 'Deep Search' },
  weather: { icon: CloudRain, color: '#0ea5e9', bg: 'bg-sky-500/10', label: 'Weather' },
  calculator: { icon: Calculator, color: '#10b981', bg: 'bg-emerald-500/10', label: 'Calculate' },
  datetime: { icon: Clock, color: '#8b5cf6', bg: 'bg-violet-500/10', label: 'Time' },
  thinking: { icon: Brain, color: '#f59e0b', bg: 'bg-amber-500/10', label: 'Think' },
  memory: { icon: FileText, color: '#6366f1', bg: 'bg-indigo-500/10', label: 'Memory' },
  url_fetch: { icon: Link, color: '#06b6d4', bg: 'bg-cyan-500/10', label: 'Fetch' },
  code_analysis: { icon: Code, color: '#ef4444', bg: 'bg-red-500/10', label: 'Analyze' },
  default: { icon: Wrench, color: '#6b7280', bg: 'bg-gray-500/10', label: 'Tool' }
} as const;

// Modern tool utilities
const getToolConfig = (toolName: string) => {
  return TOOL_CONFIG[toolName as keyof typeof TOOL_CONFIG] || TOOL_CONFIG.default;
};

const getToolIcon = (toolName: string, size: number = 10) => {
  const config = getToolConfig(toolName);
  const IconComponent = config.icon;
  
  return <IconComponent size={size} style={{ color: config.color }} className="drop-shadow-sm" />;
};

const getToolBg = (toolName: string) => {
  return getToolConfig(toolName).bg;
};

// Create proper UI components for tool results
const ToolHoverContent = ({ toolName, result, args }: { toolName: string; result: string | undefined; args: string }) => {
  if (!result) return null;

  try {
    const parsedArgs = JSON.parse(args);

    switch (toolName) {
      case 'weather': {
        const location = parsedArgs.location || parsedArgs.city;
        // Parse weather data - clean up escape characters
        const cleanResult = result.replace(/\\n/g, '\n').replace(/\\/g, '');
        const lines = cleanResult.split('\n').filter(line => line.trim());
        
        // Extract weather data with proper parsing
        const currentLine = lines.find(line => line.toLowerCase().includes('current:'));
        const feelsLikeLine = lines.find(line => line.toLowerCase().includes('feels like:'));
        const humidityLine = lines.find(line => line.toLowerCase().includes('humidity:'));
        const windLine = lines.find(line => line.toLowerCase().includes('wind:'));
        const pressureLine = lines.find(line => line.toLowerCase().includes('pressure:'));
        
        const temp = currentLine?.split(':')[1]?.split(',')[0]?.trim() || 'N/A';
        const condition = currentLine?.split(',')[1]?.trim() || 'N/A';
        const feelsLike = feelsLikeLine?.split(':')[1]?.trim() || 'N/A';
        const humidity = humidityLine?.split(':')[1]?.trim() || 'N/A';
        const wind = windLine?.split(':')[1]?.trim() || 'N/A';
        const pressure = pressureLine?.split(':')[1]?.trim() || 'N/A';
        
        return (
          <div className="space-y-3">
            <div className="text-center">
              <div className="text-2xl font-bold text-foreground">{temp}</div>
              <div className="text-sm text-muted-foreground capitalize">{condition}</div>
              {location && <div className="text-xs text-muted-foreground mt-1">{location}</div>}
            </div>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className="flex items-center gap-1.5">
                <Thermometer size={12} className="text-orange-500" />
                <span className="text-muted-foreground">Feels like:</span>
                <span className="font-medium">{feelsLike}</span>
              </div>
              <div className="flex items-center gap-1.5">
                <Droplets size={12} className="text-blue-500" />
                <span className="text-muted-foreground">Humidity:</span>
                <span className="font-medium">{humidity}</span>
              </div>
              <div className="flex items-center gap-1.5">
                <Wind size={12} className="text-gray-500" />
                <span className="text-muted-foreground">Wind:</span>
                <span className="font-medium">{wind}</span>
              </div>
              <div className="flex items-center gap-1.5">
                <Gauge size={12} className="text-purple-500" />
                <span className="text-muted-foreground">Pressure:</span>
                <span className="font-medium">{pressure}</span>
              </div>
            </div>
          </div>
        );
      }

      case 'web_search':
      case 'deep_search': {
        const query = parsedArgs.query || parsedArgs.search_term || parsedArgs.q;

        // Normalise line breaks & remove escape chars
        const clean = result.replace(/\\n/g, '\n').replace(/\\/g, '');

        // Helper to extract items `[title]: snippet (url)`
        const extractItems = (block: string) => {
          return block
            .split('\n')
            .map(l => l.trim())
            .filter(l => l && /(https?:\/\/)/.test(l))
            .map(l => {
              const match = l.match(/^(.*?):\s*(.*)\s*\((https?:[^)]+)\)$/);
              if (!match) return null;
              return { title: match[1], snippet: match[2], url: match[3] };
            })
            .filter(Boolean) as { title: string; snippet: string; url: string }[];
        };

        // Build sections – deep_search has multiple blocks separated by ===
        const sections: { heading?: string; items: any[] }[] = [];
        if (toolName === 'deep_search') {
          const parts = clean.split(/=== Results for "([^"]+)" ===/g);
          for (let i = 0; i < parts.length; i += 2) {
            const heading = parts[i]?.trim();
            const content = parts[i + 1] || '';
            if (!content) continue;
            sections.push({ heading: heading || undefined, items: extractItems(content) });
          }
        } else {
          sections.push({ items: extractItems(clean.replace(/^Search results.*?\n/, '')) });
        }

        return (
          <div className="space-y-3">
            {query && (
              <div className="text-xs text-muted-foreground">
                <span className="font-medium">Query:</span> "{query}"
              </div>
            )}
            {sections.map((sec, idx) => (
              <div key={idx} className="space-y-1">
                {sec.heading && (
                  <div className="text-[10px] uppercase tracking-wide text-muted-foreground font-semibold">
                    {sec.heading}
                  </div>
                )}
                <ul className="space-y-1 max-h-36 overflow-y-auto pr-1">
                  {sec.items.slice(0, 10).map((item, i) => (
                    <li key={i} className="text-xs leading-snug">
                      <a
                        href={item.url}
                        target="_blank"
                        rel="noreferrer"
                        className="font-medium text-primary hover:underline"
                      >
                        {item.title}
                      </a>
                      {item.snippet && (
                        <span className="text-muted-foreground"> – {item.snippet}</span>
                      )}
                    </li>
                  ))}
                  {sec.items.length === 0 && (
                    <li className="text-xs text-muted-foreground">No results parsed.</li>
                  )}
                </ul>
              </div>
            ))}
          </div>
        );
      }

      case 'calculator': {
        const expression = parsedArgs.expression || parsedArgs.calculation || parsedArgs.query;
        // Clean up escape characters
        const cleanResult = result.replace(/\\n/g, '\n').replace(/\\/g, '');
        
        return (
          <div className="space-y-2 text-center">
            {expression && (
              <div className="text-xs text-muted-foreground font-mono bg-muted/30 p-2 rounded">
                {expression}
              </div>
            )}
            <div className="text-lg font-bold text-foreground font-mono">
              = {cleanResult}
            </div>
          </div>
        );
      }

      case 'datetime': {
        // Clean up escape characters
        const cleanResult = result.replace(/\\n/g, '\n').replace(/\\/g, '');
        return (
          <div className="text-center">
            <div className="text-sm font-mono bg-muted/30 p-2 rounded">
              {cleanResult.split('\n').map((line, i) => (
                <div key={i} className={i === 0 ? 'font-bold' : 'text-muted-foreground'}>
                  {line.trim() || '\u00A0'}
                </div>
              ))}
            </div>
          </div>
        );
      }

      case 'code_analysis': {
        const language = parsedArgs.language || parsedArgs.lang;
        // Clean up escape characters
        const cleanResult = result.replace(/\\n/g, '\n').replace(/\\/g, '');
        const truncatedResult = cleanResult.length > 400 ? `${cleanResult.substring(0, 400)}...` : cleanResult;
        
        return (
          <div className="space-y-2">
            {language && (
              <div className="text-xs text-muted-foreground">
                <span className="font-medium">Language:</span> {language}
              </div>
            )}
            <pre className="text-[10px] font-mono bg-muted/30 p-2 rounded border overflow-x-auto">
              {truncatedResult.split('\n').map((line, i) => (
                <div key={i} className={line.trim() ? '' : 'h-1'}>
                  {line.trim() || '\u00A0'}
                </div>
              ))}
            </pre>
          </div>
        );
      }

      case 'url_fetch': {
        const url = parsedArgs.url || parsedArgs.link;
        let domain = 'URL';
        try {
          domain = new URL(url).hostname.replace('www.', '');
        } catch {
          domain = 'URL';
        }
        // Clean up escape characters
        const cleanResult = result.replace(/\\n/g, '\n').replace(/\\/g, '');
        const truncatedResult = cleanResult.length > 350 ? `${cleanResult.substring(0, 350)}...` : cleanResult;
        
        return (
          <div className="space-y-2">
            <div className="flex items-center gap-1.5 text-xs text-muted-foreground">
              <Link size={10} />
              <span className="font-medium">{domain}</span>
            </div>
            <div className="text-xs leading-relaxed bg-muted/30 p-2 rounded border max-h-32 overflow-y-auto">
              {truncatedResult.split('\n').map((line, i) => (
                <div key={i} className={line.trim() ? '' : 'h-1'}>
                  {line.trim() || '\u00A0'}
                </div>
              ))}
            </div>
          </div>
        );
      }

      default: {
        // Clean up escape characters
        const cleanResult = result.replace(/\\n/g, '\n').replace(/\\/g, '');
        const truncatedResult = cleanResult.length > 300 ? `${cleanResult.substring(0, 300)}...` : cleanResult;
        
        return (
          <div className="text-xs leading-relaxed bg-muted/30 p-2 rounded border max-h-32 overflow-y-auto">
            {truncatedResult.split('\n').map((line, i) => (
              <div key={i} className={line.trim() ? '' : 'h-1'}>
                {line.trim() || '\u00A0'}
              </div>
            ))}
          </div>
        );
      }
    }
  } catch {
    // Clean up escape characters
    const cleanResult = result.replace(/\\n/g, '\n').replace(/\\/g, '');
    const truncatedResult = cleanResult.length > 300 ? `${cleanResult.substring(0, 300)}...` : cleanResult;
    
    return (
      <div className="text-xs leading-relaxed bg-muted/30 p-2 rounded border max-h-32 overflow-y-auto">
        {truncatedResult.split('\n').map((line, i) => (
          <div key={i} className={line.trim() ? '' : 'h-1'}>
            {line.trim() || '\u00A0'}
          </div>
        ))}
      </div>
    );
  }
};

// Format tool result for hover card display
const formatToolResultForHover = (toolName: string, result: string | undefined, args: string) => {
  if (!result) return null;
  
  try {
    const parsedArgs = JSON.parse(args);
    
    switch (toolName) {
      case 'web_search':
      case 'deep_search': {
        const query = parsedArgs.query || parsedArgs.search_term || parsedArgs.q;
        return {
          title: `Search Results${query ? ` for "${query}"` : ''}`,
          type: 'search'
        };
      }
      
      case 'weather': {
        const location = parsedArgs.location || parsedArgs.city;
        return {
          title: `Weather${location ? ` in ${location}` : ''}`,
          type: 'weather'
        };
      }
      
      case 'calculator': {
        const expression = parsedArgs.expression || parsedArgs.calculation || parsedArgs.query;
        return {
          title: `Calculation${expression ? ` of "${expression}"` : ''}`,
          type: 'calculation'
        };
      }
      
      case 'url_fetch': {
        const url = parsedArgs.url || parsedArgs.link;
        let domain = 'URL';
        try {
          domain = new URL(url).hostname.replace('www.', '');
        } catch {
          domain = 'URL';
        }
        return {
          title: `Content from ${domain}`,
          type: 'content'
        };
      }
      
      case 'memory': {
        const key = parsedArgs.key || parsedArgs.topic;
        return {
          title: `Memory${key ? ` - ${key}` : ''}`,
          type: 'memory'
        };
      }
      
      case 'datetime': {
        return {
          title: 'Date & Time',
          type: 'time'
        };
      }
      
      case 'code_analysis': {
        const language = parsedArgs.language || parsedArgs.lang;
        return {
          title: `Code Analysis${language ? ` (${language})` : ''}`,
          type: 'code'
        };
      }
      
      default: {
        return {
          title: toolName.replace(/_/g, ' '),
          type: 'generic'
        };
      }
    }
  } catch {
    return {
      title: toolName.replace(/_/g, ' '),
      type: 'generic'
    };
  }
};

const getToolDetails = (toolName: string, args: string) => {
  try {
    const parsedArgs = JSON.parse(args);
    const config = getToolConfig(toolName);
    
    switch (toolName) {
      case 'web_search':
      case 'deep_search': {
        const query = parsedArgs.query || parsedArgs.search_term || parsedArgs.q;
        if (query) {
          const truncated = query.length > 25 ? `${query.substring(0, 25)}...` : query;
          return { label: config.label, detail: `"${truncated}"` };
        }
        return { label: config.label, detail: null };
      }
        
      case 'weather': {
        const location = parsedArgs.location || parsedArgs.city;
        if (location) {
          return { label: config.label, detail: location };
        }
        return { label: config.label, detail: null };
      }
        
      case 'calculator': {
        const expression = parsedArgs.expression || parsedArgs.calculation || parsedArgs.query;
        if (expression) {
          const truncated = expression.length > 20 ? `${expression.substring(0, 20)}...` : expression;
          return { label: config.label, detail: truncated };
        }
        return { label: config.label, detail: null };
      }
        
      case 'url_fetch': {
        const url = parsedArgs.url || parsedArgs.link;
        if (url) {
          try {
            const urlObj = new URL(url);
            const domain = urlObj.hostname.replace('www.', '');
            return { label: config.label, detail: domain };
          } catch {
            return { label: config.label, detail: 'URL' };
          }
        }
        return { label: config.label, detail: null };
      }
        
      case 'memory': {
        const key = parsedArgs.key || parsedArgs.topic;
        if (key) {
          const truncated = key.length > 15 ? `${key.substring(0, 15)}...` : key;
          return { label: config.label, detail: truncated };
        }
        return { label: config.label, detail: null };
      }
        
      case 'datetime': {
        const timezone = parsedArgs.timezone || parsedArgs.tz;
        if (timezone) {
          return { label: config.label, detail: timezone };
        }
        return { label: 'Date & Time', detail: null };
      }
        
      case 'code_analysis': {
        const language = parsedArgs.language || parsedArgs.lang;
        if (language) {
          return { label: config.label, detail: language };
        }
        return { label: 'Code Analysis', detail: null };
      }
        
      default: {
        // For unknown tools, try to extract any meaningful first argument
        const firstValue = Object.values(parsedArgs)[0];
        if (typeof firstValue === 'string' && firstValue.length > 0) {
          const truncated = firstValue.length > 20 ? `${firstValue.substring(0, 20)}...` : firstValue;
          return { label: toolName.replace(/_/g, ' '), detail: truncated };
        }
        return { label: toolName.replace(/_/g, ' '), detail: null };
      }
    }
  } catch {
    // If args can't be parsed, just return the tool name
    return { label: toolName.replace(/_/g, ' '), detail: null };
  }
};

// Ultra-modern inline badge for a single tool with hover card
const InlineToolBadge = memo(({ call }: { call: ToolCall }) => {
  const { label, detail } = getToolDetails(call.name, call.arguments);
  const config = getToolConfig(call.name);
  const hoverData = formatToolResultForHover(call.name, call.result, call.arguments);

  const badgeContent = (
    <span className={cn(
      "inline-flex items-center gap-1.5 px-2.5 py-1 text-xs font-medium rounded-full transition-all duration-300",
      "border border-border/40 bg-card/80 backdrop-blur-sm cursor-pointer shadow-sm",
      "hover:shadow-md hover:scale-[1.02] hover:border-border/60 hover:bg-card/90",
      "active:scale-[0.98] active:shadow-sm"
    )}>
      {/* Icon with subtle background */}
      <div className={cn(
        "flex items-center justify-center w-4 h-4 rounded-full transition-colors",
        config?.bg || "bg-muted/50"
      )}>
        {getToolIcon(call.name, 10)}
      </div>
      
      {/* Label with better typography */}
      <span className="text-foreground font-semibold tracking-tight">
        {label}
      </span>
      
      {/* Detail with improved styling */}
      {detail && (
        <>
          <span className="text-muted-foreground/40 mx-0.5">•</span>
          <span className="text-muted-foreground font-mono text-[10px] max-w-[80px] truncate">
            {detail}
          </span>
        </>
      )}
    </span>
  );

  // If no tool result, just return the badge without hover card
  if (!hoverData) {
    return badgeContent;
  }

  return (
    <HoverCard openDelay={300} closeDelay={100}>
      <HoverCardTrigger asChild>
        {badgeContent}
      </HoverCardTrigger>
      <HoverCardContent className="w-80 p-0 overflow-hidden" side="top">
        {/* Header with gradient */}
        <div className={cn(
          "px-4 py-3 border-b bg-gradient-to-r",
          config?.bg?.replace('bg-', 'from-') || "from-muted/20",
          "to-transparent"
        )}>
          <div className="flex items-center gap-2">
            <div className={cn(
              "flex items-center justify-center w-5 h-5 rounded-md",
              config?.bg || "bg-muted/50"
            )}>
              {getToolIcon(call.name, 12)}
            </div>
            <h4 className="text-sm font-semibold text-foreground">{hoverData.title}</h4>
          </div>
        </div>
        
        {/* Content with better styling */}
        <div className="p-4 max-h-40 overflow-y-auto">
          <ToolHoverContent 
            toolName={call.name} 
            result={call.result} 
            args={call.arguments} 
          />
        </div>
      </HoverCardContent>
    </HoverCard>
  );
});
InlineToolBadge.displayName = 'InlineToolBadge';

// Ultra-modern component for rendering multiple inline tools
const InlineToolsGroup = memo(({ toolCalls }: { toolCalls: ToolCall[] }) => {
  if (toolCalls.length === 0) return null;
  
  // Single tool - simple inline badge
  if (toolCalls.length === 1) {
    return <InlineToolBadge call={toolCalls[0]} />;
  }

  // 2-3 tools - sleek horizontal group
  if (toolCalls.length <= 3) {
    return (
      <div className="inline-flex items-center gap-1 p-1 bg-muted/5 rounded-lg border border-border/20 backdrop-blur-sm hover:bg-muted/10 transition-all duration-200">
        {toolCalls.map((call) => (
          <InlineToolBadge key={call.id} call={call} />
        ))}
      </div>
    );
  }

  // 4+ tools - modern pill layout with counts
  const toolGroups: Array<{ tool: string; count: number; calls: ToolCall[] }> = [];
  let currentGroup: { tool: string; count: number; calls: ToolCall[] } | null = null;

  toolCalls.forEach(call => {
    if (currentGroup && currentGroup.tool === call.name) {
      currentGroup.count++;
      currentGroup.calls.push(call);
    } else {
      if (currentGroup) {
        toolGroups.push(currentGroup);
      }
      currentGroup = { tool: call.name, count: 1, calls: [call] };
    }
  });
  
  if (currentGroup) {
    toolGroups.push(currentGroup);
  }

  const totalTools = toolCalls.length;
  const uniqueTypes = toolGroups.length;

  return (
    <div className="inline-block">
      {/* Ultra-minimal header */}
      <div className="flex items-center gap-1 mb-1.5 text-xs text-muted-foreground/70 font-medium">
        <div className="w-1 h-1 rounded-full bg-muted-foreground/30"></div>
        <span>{totalTools} tools</span>
      </div>

      {/* Modern pill grid */}
      <div className="flex flex-wrap gap-1">
        {toolGroups.map((group, index) => (
          <div key={`${group.tool}-${index}`} className={cn(
            "inline-flex items-center gap-1 px-1.5 py-0.5 rounded-md text-xs font-medium transition-all duration-200",
            "hover:scale-105 border-0",
            getToolBg(group.tool)
          )}>
            {/* Minimal icon */}
            <div className="flex items-center justify-center w-3 h-3">
              {getToolIcon(group.tool, 8)}
            </div>
            
            {/* Tool name */}
            <span className="text-foreground/90 font-semibold tracking-tight">
              {getToolConfig(group.tool).label}
            </span>
            
            {/* Count badge */}
            {group.count > 1 && (
              <span className="text-[10px] px-1 py-0.5 bg-foreground/10 text-foreground/80 rounded font-bold ml-0.5">
                {group.count}
              </span>
            )}
          </div>
        ))}
      </div>
    </div>
  );
});
InlineToolsGroup.displayName = 'InlineToolsGroup';
 
 const RowToolCallsGroup = memo(({ toolCalls }: { toolCalls: ToolCall[] }) => {
  const grouped = useMemo(() => groupToolCalls(toolCalls), [toolCalls]);
  return (
    <div className="flex flex-wrap gap-2 overflow-x-auto py-1">
      {grouped.map(g => <ToolGroupBadge key={g.name} group={g} />)}
    </div>
  );
});
RowToolCallsGroup.displayName='RowToolCallsGroup';

export const MessageBubble = memo(function MessageBubble({ message, messagePosition, currentBranchId, isStreaming, isSharedView, onCopyMessage, onEditMessage, onRetryMessage, onBranchOff, onSwitchBranch }: MessageBubbleProps) {
  const preferences = useQuery(api.preferences.get);
  const [isEditing, setIsEditing] = useState(false);
  const [editedContent, setEditedContent] = useState(message.content);
  const [isHovered, setIsHovered] = useState(false);
  
  // Debounce streaming content to reduce rerenders
  const debouncedContent = useDebounce(message.content, 120);
  const debouncedSequence = useDebounce((message as any).contentSequence, 120);
  
  // Memoize computed values to prevent recalculation
  const messageFlags = useMemo(() => ({
    isUser: message.role === "user",
    isAssistant: message.role === "assistant",
    isTool: message.role === "tool",
    isError: message.isError
  }), [message.role, message.isError]);
  
  const { isUser, isAssistant, isTool, isError } = messageFlags;

  const groupedToolCalls = useMemo(() => 
    message.toolCalls ? groupToolCalls(message.toolCalls) : [], 
    [message.toolCalls]
  );

  // Build a concise summary of the model's actions (thinking time + tool usage)
  const toolUsageSummary = useMemo(() => {
    if (!groupedToolCalls.length) return null;

    const parts: string[] = [];

    // Include thinking time only if actual reasoning occurred
    if (message.thinking && message.generationMetrics?.reasoningTimeMs && message.generationMetrics.reasoningTimeMs > 0) {
      const seconds = (message.generationMetrics.reasoningTimeMs / 1000).toFixed(1);
      parts.push(`thought for ${seconds}s`);
    }

    // Describe each tool usage with better formatting
    const toolDescriptions: string[] = [];
    groupedToolCalls.forEach((group) => {
      const name = group.name.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase());
      if (group.count === 1) {
        toolDescriptions.push(name);
      } else {
        toolDescriptions.push(`${name} (${group.count}×)`);
      }
    });

    if (toolDescriptions.length > 0) {
      if (toolDescriptions.length === 1) {
        parts.push(`used ${toolDescriptions[0]}`);
      } else if (toolDescriptions.length === 2) {
        parts.push(`used ${toolDescriptions[0]} and ${toolDescriptions[1]}`);
      } else {
        const lastTool = toolDescriptions.pop();
        parts.push(`used ${toolDescriptions.join(', ')}, and ${lastTool}`);
      }
    }

    if (!parts.length) return null;

    const sentence = parts.join(', then ');
    return sentence.charAt(0).toUpperCase() + sentence.slice(1) + '.';
  }, [groupedToolCalls, message.generationMetrics, message.thinking]);

  // Support multiple canvases per message
  const canvases: CanvasData[] = useMemo(() => {
    const list: CanvasData[] = [];

    // 1. Canvases from tool calls "canvas"
    if (message.toolCalls?.length) {
      for (const tc of message.toolCalls) {
        if (tc.name === "canvas" && tc.result) {
          try {
            const parsed = JSON.parse(tc.result);
            if (parsed && typeof parsed === "object" && parsed.type && parsed.title) {
              list.push(parsed as CanvasData);
            }
          } catch {
            /* ignore parsing errors */
          }
        }
      }
    }

    // 2. Canvases stored in canvasData field
    if (message.canvasData) {
      if (Array.isArray(message.canvasData)) {
        list.push(...(message.canvasData as CanvasData[]));
      } else {
        list.push(message.canvasData as CanvasData);
      }
    }

    // Sort by updatedAt ascending to keep chronological order
    list.sort((a, b) => a.updatedAt - b.updatedAt);
    return list;
  }, [message.toolCalls, message.canvasData]);
  
  // Helper function to format generation metrics with enhanced details
  const formatMetrics = useCallback((metrics: typeof message.generationMetrics) => {
    if (!metrics) return null;

    const parts = [];

    // Provider and model
    if (metrics.provider && metrics.model) {
      parts.push(`${metrics.provider}/${metrics.model}`);
    }

    // Token information - show both completion and total if available
    if (metrics.completionTokens || metrics.tokensUsed) {
      const completionTokens = metrics.completionTokens || 0;
      const totalTokens = metrics.tokensUsed || completionTokens;
      const promptTokens = metrics.promptTokens || (totalTokens - completionTokens);

      if (promptTokens > 0 && completionTokens > 0) {
        parts.push(`${promptTokens + completionTokens} tokens (${promptTokens} in, ${completionTokens} out)`);
      } else if (totalTokens > 0) {
        parts.push(`${totalTokens} tokens`);
      }
    }

    // Generation time with enhanced timing details
    if (metrics.generationTimeMs) {
      const seconds = (metrics.generationTimeMs / 1000).toFixed(1);
      parts.push(`${seconds}s total`);
    }

    // Time to first token if available
    if (metrics.timeToFirstTokenMs) {
      const ttftSeconds = (metrics.timeToFirstTokenMs / 1000).toFixed(2);
      parts.push(`${ttftSeconds}s TTFT`);
    }

    // Tokens per second
    if (metrics.tokensPerSecond && metrics.tokensPerSecond > 0) {
      parts.push(`${metrics.tokensPerSecond.toFixed(1)} t/s`);
    }

    return parts.join(' • ');
  }, []);

  // Helper function to format detailed metadata for expanded view
  const formatDetailedMetrics = useCallback((metrics: typeof message.generationMetrics) => {
    if (!metrics) return null;

    const details = [];

    if (metrics.timeToFirstTokenMs) {
      const ttftSeconds = (metrics.timeToFirstTokenMs / 1000).toFixed(2);
      details.push(`TTFT ${ttftSeconds}s`);
    }

    if (metrics.reasoningTimeMs) {
      const reasoningSeconds = (metrics.reasoningTimeMs / 1000).toFixed(1);
      details.push(`reasoning ${reasoningSeconds}s`);
    }

    if (metrics.toolExecutionTimeMs) {
      const toolSeconds = (metrics.toolExecutionTimeMs / 1000).toFixed(1);
      details.push(`tools ${toolSeconds}s`);
    }

    return details.length > 0 ? details.join(' • ') : null;
  }, []);

  const handleSaveEdit = useCallback(() => {
    if (editedContent.trim() !== message.content) {
      void onEditMessage?.(message._id, editedContent.trim());
    }
    setIsEditing(false);
  }, [editedContent, message.content, message._id, onEditMessage]);

  const handleCancelEdit = useCallback(() => {
    setEditedContent(message.content);
    setIsEditing(false);
  }, [message.content]);

  const handleStartEdit = useCallback(() => {
    setEditedContent(message.content);
    setIsEditing(true);
  }, [message.content]);

  // Memoized callback handlers to prevent re-renders
  const handleCopyMessage = useCallback(() => {
    void onCopyMessage?.(message._id);
  }, [onCopyMessage, message._id]);

  const handleRetryMessage = useCallback(() => {
    void onRetryMessage?.(message._id);
  }, [onRetryMessage, message._id]);

  const handleBranchOffWithModel = useCallback((provider?: string, model?: string) => {
    void onBranchOff?.(message._id, provider, model);
  }, [onBranchOff, message._id]);

  const handleMouseEnter = useCallback(() => {
    setIsHovered(true);
  }, []);

  const handleMouseLeave = useCallback(() => {
    setIsHovered(false);
  }, []);

  if (isTool && preferences?.showToolOutputs) {
    // Parse the tool content to extract tool name and result
    let toolName = "Tool";
    let toolResult = message.content;
    
    // Try to extract tool name from common patterns
    const weatherMatch = message.content.match(/Weather for ([^:]+):/);
    const searchMatch = message.content.match(/Search results for "([^"]+)":/);
    
    if (weatherMatch) {
      toolName = "Weather";
      toolResult = message.content;
    } else if (searchMatch) {
      toolName = "Web Search";
      toolResult = message.content;
    } else if (message.content.includes("Current date and time:")) {
      toolName = "Date & Time";
    } else if (message.content.includes("Result:")) {
      toolName = "Calculator";
    }

    return (
      <div className="flex items-start gap-3 max-w-4xl mx-auto px-4 py-3">
        <div className="p-2 bg-blue-500/10 rounded-lg mt-1 flex-shrink-0">
          <Terminal size={16} className="text-blue-600 dark:text-blue-400" />
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-3">
            <span className="text-sm font-medium text-blue-600 dark:text-blue-400">
              {toolName}
            </span>
            <span className="text-xs text-muted-foreground px-2 py-1 bg-muted/50 rounded-full">
              Tool Output
            </span>
          </div>
          <div className="bg-gradient-to-br from-muted/30 to-muted/50 rounded-xl p-4 border border-border/50">
            <MemoizedMarkdown content={toolResult} />
          </div>
        </div>
      </div>
    );
  }

  if (isTool && !preferences?.showToolOutputs) {
    return null;
  }

  return (
    <div className={cn('flex items-start gap-2 sm:gap-4 group', isUser && 'justify-end')}>
      <div
        className={cn(
          isAssistant ? 'w-full' : 'max-w-full sm:max-w-[75%]',
          'space-y-3',
          isUser ? 'order-1 items-end' : 'order-2 items-start'
        )}
      >
        {/* Message Version Selector - Show for assistant messages when there are multiple branches */}
        {!isSharedView && isAssistant && messagePosition !== undefined && currentBranchId && onSwitchBranch && (
          <MessageVersionSelector
            conversationId={message.conversationId}
            currentBranchId={currentBranchId}
            messagePosition={messagePosition}
            onSwitchBranch={onSwitchBranch}
          />
        )}

                <Card
          className={cn(
            "px-4 sm:px-6 py-3 sm:py-4 shadow-sm backdrop-blur-sm transition-all duration-200 break-words overflow-hidden",
            isUser
              ? 'bg-muted/80 text-foreground font-medium rounded-xl rounded-br-md border border-border/40 backdrop-blur-sm relative overflow-hidden px-3 py-2'
              : isError
              ? 'bg-red-50/70 dark:bg-red-950/40 border-2 border-red-300 dark:border-red-700 rounded-3xl rounded-bl-lg shadow-lg ring-2 ring-red-200/50 dark:ring-red-800/50'
              : 'bg-transparent rounded-3xl rounded-bl-lg border-none shadow-none'
          )}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          {/* Attachments rendering */}
          {message.attachments && message.attachments.length > 0 && (
            <div className={cn(
              'mb-3 space-y-3',
              isAssistant && 'w-full sm:w-4/5'
            )}>
             {message.attachments.map((attachment, index) => (
               <div key={`attachment-${index}`}>
                 <AttachmentRenderer attachment={attachment} isSharedView={isSharedView} />
               </div>
             ))}
           </div>
          )}

          {/* Reasoning content rendering - Only show if enabled and thinking exists */}
          {message.thinking && preferences?.showThinking && (
            <div className={cn('mb-3', isAssistant && 'w-full sm:w-4/5')}>
              <ReasoningSection message={message} isStreaming={isStreaming} />
            </div>
          )}

          {/* Minimal Error Display */}
        {isError && (
          <div className="mb-3 p-3 bg-red-50/60 dark:bg-red-950/30 border border-red-200 dark:border-red-800 rounded-lg">
            <div className="flex items-center justify-between gap-3">
              <div className="flex items-center gap-2 flex-1 min-w-0">
                <div className="flex-shrink-0 w-5 h-5 bg-red-100 dark:bg-red-900/50 rounded-full flex items-center justify-center">
                  <X size={12} className="text-red-600 dark:text-red-400" />
                </div>
                <div className="flex-1 min-w-0">
                  <span className="text-sm font-medium text-red-800 dark:text-red-200">
                    {getUltraMinimalError(message.content)}
                  </span>
                </div>
              </div>
              
              {/* Compact retry button */}
              {!isSharedView && isAssistant && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-7 w-7 p-0 text-red-600 dark:text-red-400 hover:bg-red-100 dark:hover:bg-red-900/50"
                  onClick={handleRetryMessage}
                >
                  <RotateCcw size={12} />
                </Button>
              )}
            </div>
          </div>
        )}

        {/* Message Content - Editable for User Messages */}
          {isEditing && isUser ? (
            <div className="space-y-3">
              <Textarea
                value={editedContent}
                onChange={(e) => setEditedContent(e.target.value)}
                className="min-h-[100px] resize-none border-border/50 focus:border-primary/50"
                placeholder="Edit your message..."
                autoFocus
              />
              <div className="flex items-center gap-2">
                <Button
                  size="sm"
                  onClick={handleSaveEdit}
                  className="h-8 px-3"
                >
                  <Check size={14} className="mr-1" />
                  Save
                </Button>
                <Button
                  size="sm"
                  variant="secondary"
                  onClick={handleCancelEdit}
                  className="h-8 px-3"
                >
                  <X size={14} className="mr-1" />
                  Cancel
                </Button>
              </div>
            </div>
          ) : (
            <div className={cn(
              "message-content w-full",
              isError && "text-red-900 dark:text-red-100 bg-red-50/70 dark:bg-red-950/40 p-4 rounded-xl border-2 border-red-300/50 dark:border-red-700/50 shadow-sm font-medium"
            )}>
              {/* User message truncation */}
              {isUser && !isEditing ? (() => {
                const needsTruncate = message.content.length > 400 || message.content.split('\n').length > 6;
                
                if (needsTruncate) {
                  return (
                    <div className={cn(
                      'markdown-content prose prose-base max-w-none break-words',
                      'prose-p:mb-4 prose-p:mt-0 prose-p:leading-relaxed',
                      'prose-ul:mb-4 prose-ol:mb-4 prose-li:mb-1',
                      'prose-h1:mb-4 prose-h2:mb-3 prose-h3:mb-3 prose-h4:mb-2',
                      'prose-pre:mb-4 prose-code:text-sm',
                      isUser && 'text-foreground prose-headings:text-foreground prose-strong:text-foreground prose-code:text-foreground/90 prose-code:bg-foreground/10 prose-code:px-1.5 prose-code:py-0.5 prose-code:rounded-md prose-code:font-medium prose-blockquote:text-foreground/80 prose-blockquote:border-foreground/30 prose-a:text-foreground prose-a:underline prose-a:decoration-foreground/50 hover:prose-a:decoration-foreground/80 prose-ul:text-foreground prose-ol:text-foreground prose-li:text-foreground'
                    )}>
                      <TruncatedContent content={message.content} maxLines={4} />
                    </div>
                  );
                }
                
                return (
                  <div className={cn(
                    'markdown-content prose prose-base max-w-none break-words',
                    'prose-p:mb-4 prose-p:mt-0 prose-p:leading-relaxed',
                    'prose-ul:mb-4 prose-ol:mb-4 prose-li:mb-1',
                    'prose-h1:mb-4 prose-h2:mb-3 prose-h3:mb-3 prose-h4:mb-2',
                    'prose-pre:mb-4 prose-code:text-sm',
                    isUser && 'text-background prose-headings:text-background prose-strong:text-background prose-code:text-background/90 prose-code:bg-background/20 prose-code:px-1.5 prose-code:py-0.5 prose-code:rounded-md prose-code:font-medium prose-blockquote:text-background/80 prose-blockquote:border-background/30 prose-a:text-background/90 prose-a:underline prose-a:decoration-background/50 hover:prose-a:decoration-background/80 prose-ul:text-background prose-ol:text-background prose-li:text-background'
                  )}>
                    <MemoizedMarkdown content={message.content} />
                  </div>
                );
              })() : (
                <div className={cn(
                  'markdown-content prose prose-base max-w-none break-words',
                  'prose-p:mb-4 prose-p:mt-0 prose-p:leading-relaxed',
                  'prose-ul:mb-4 prose-ol:mb-4 prose-li:mb-1',
                  'prose-h1:mb-4 prose-h2:mb-3 prose-h3:mb-3 prose-h4:mb-2',
                  'prose-pre:mb-4 prose-code:text-sm',
                  isUser && 'text-white prose-headings:text-white prose-strong:text-white prose-code:text-blue-100 prose-code:bg-white/10 prose-code:px-1.5 prose-code:py-0.5 prose-code:rounded-md prose-code:font-medium prose-blockquote:text-white/90 prose-blockquote:border-white/30 prose-a:text-blue-100 prose-a:underline prose-a:decoration-blue-200/50 hover:prose-a:decoration-blue-200 prose-ul:text-white prose-ol:text-white prose-li:text-white'
                )}>
                  {(() => {
                    // Use the actual content sequence if available (new accurate method)
                    if (debouncedSequence && message.toolCalls) {
                      const sequence = debouncedSequence;
                    const toolCallsMap = new Map(
                      (message.toolCalls as ToolCall[]).map(call => [call.id, call])
                    );
                    
                    // Group consecutive tool items for better rendering
                    const processedSequence: Array<{
                      type: "content" | "tools";
                      content?: string;
                      toolCalls?: ToolCall[];
                      idx: number;
                    }> = [];

                    let i = 0;
                    while (i < sequence.length) {
                      const item = sequence[i];
                      
                      if (item.type === "content") {
                        processedSequence.push({
                          type: "content",
                          content: item.text,
                          idx: i
                        });
                        i++;
                      } else if (item.type === "tool") {
                        // Group consecutive tool calls
                        const groupedTools: ToolCall[] = [];
                        while (i < sequence.length && sequence[i].type === "tool") {
                          const toolCall = toolCallsMap.get(sequence[i].toolCallId);
                          if (toolCall) {
                            groupedTools.push(toolCall);
                          }
                          i++;
                        }
                        
                        if (groupedTools.length > 0) {
                          processedSequence.push({
                            type: "tools",
                            toolCalls: groupedTools,
                            idx: i - groupedTools.length
                          });
                        }
                      } else {
                        i++;
                      }
                    }
                    
                    const hasContent = processedSequence.some(item => item.type === "content" && item.content?.trim());
                    const hasTools = processedSequence.some(item => item.type === "tools");
                    
                    return (
                      <div className={cn(
                        "w-full",
                        !hasContent && hasTools && "flex flex-wrap items-center gap-2 p-3 sm:p-4 bg-gradient-to-r from-muted/20 to-muted/10 rounded-xl border border-border/30 shadow-sm"
                      )}>
                        {processedSequence.map((item) => {
                          if (item.type === "content") {
                            // Don't trim content chunks - preserve markdown formatting
                            if (item.content) {
                              // Detect and skip rendering JSON tool-call payloads that immediately precede an inline tool badge.
                              try {
                                const raw = item.content.trim();

                                // Remove code block markers if present
                                const stripped = raw.replace(/^```(?:json)?\s*\n?|\n?```$/g, "").trim();

                                // Check if this looks like a JSON object
                                if (
                                  (stripped.startsWith("{") && stripped.endsWith("}")) ||
                                  (stripped.startsWith("[") && stripped.endsWith("]"))
                                ) {
                                  // Try to parse as JSON
                                  const obj = JSON.parse(stripped);

                                  // Check for various tool call patterns
                                  const looksLikeTool =
                                    // Standard tool call format
                                    (obj && typeof obj === "object" && "name" in obj && "arguments" in obj) ||
                                    // Function call format
                                    (obj && typeof obj === "object" && "function" in obj) ||
                                    // Tool invocation format
                                    (obj && typeof obj === "object" && "tool" in obj) ||
                                    // Canvas/Code tool call format
                                    (obj && typeof obj === "object" &&
                                      "title" in obj && typeof obj.title === "string" &&
                                      "type" in obj && typeof obj.type === "string" &&
                                      "content" in obj && typeof obj.content === "string") ||
                                    // Weather tool specific format
                                    (obj && typeof obj === "object" && "date" in obj && "forecast_type" in obj && "location" in obj) ||
                                    // Search tool format
                                    (obj && typeof obj === "object" && "query" in obj && typeof obj.query === "string") ||
                                    // Calculator tool format
                                    (obj && typeof obj === "object" && "expression" in obj) ||
                                    // Generic tool parameter format (object with typical tool parameter names)
                                    (obj && typeof obj === "object" &&
                                      (("input" in obj) || ("prompt" in obj) || ("text" in obj) || ("url" in obj) || ("code" in obj))
                                    ) ||
                                    // Array of tool calls
                                    (Array.isArray(obj) && obj.length > 0 && obj.every(item =>
                                      item && typeof item === "object" && ("name" in item || "function" in item || "tool" in item)
                                    ));

                                  // Hide raw JSON tool-call payloads from assistant messages, even if we didn't successfully record a ToolCall
                                  if (looksLikeTool && message.role === "assistant") {
                                    return null; // Skip showing raw JSON tool request payload
                                  }
                                }
                              } catch {
                                /* ignore parse errors */
                              }
                              return (
                                <MemoizedMarkdown key={`content-${item.idx}`} content={item.content} />
                              );
                            }
                            return null;
                          } else if (item.type === "tools") {
                            return (
                              <div key={`tools-${item.idx}`} className={cn(
                                hasContent ? "block my-3 sm:my-4" : "inline-block"
                              )}>
                                <InlineToolsGroup toolCalls={item.toolCalls || []} />
                              </div>
                            );
                          }
                          return null;
                        })}
                        {isStreaming && (
                          <div className={cn(hasContent ? "inline-block" : "ml-2")}>
                            <LoadingDots 
                              size="sm" 
                              variant={hasContent ? "fade" : "wave"} 
                              color="muted" 
                            />
                          </div>
                        )}
                      </div>
                    );
                  }
                  
                    // Fallback for messages without contentSequence (legacy messages)

                    // Detect and skip pure tool-call JSON payloads to avoid showing raw JSON before the tool badges.
                    try {
                      if (
                        message.toolCalls?.length &&
                        typeof debouncedContent === "string"
                      ) {
                        // Remove code block markers if present
                        const trimmed = debouncedContent.trim().replace(/^```(?:json)?\s*\n?|\n?```$/g, "").trim();

                        // Check if this looks like a JSON object or array
                        if (
                          (trimmed.startsWith("{") && trimmed.endsWith("}")) ||
                          (trimmed.startsWith("[") && trimmed.endsWith("]"))
                        ) {
                          const parsed = JSON.parse(trimmed);

                          // Check for various tool call patterns
                          const looksLikeTool =
                            // Standard tool call format
                            (parsed && typeof parsed === "object" && "name" in parsed && "arguments" in parsed) ||
                            // Function call format
                            (parsed && typeof parsed === "object" && "function" in parsed) ||
                            // Tool invocation format
                            (parsed && typeof parsed === "object" && "tool" in parsed) ||
                            // Canvas/Code tool call format
                            (parsed && typeof parsed === "object" &&
                              "title" in parsed && typeof parsed.title === "string" &&
                              "type" in parsed && typeof parsed.type === "string" &&
                              "content" in parsed && typeof parsed.content === "string") ||
                            // Weather tool specific format
                            (parsed && typeof parsed === "object" && "date" in parsed && "forecast_type" in parsed && "location" in parsed) ||
                            // Search tool format
                            (parsed && typeof parsed === "object" && "query" in parsed && typeof parsed.query === "string") ||
                            // Calculator tool format
                            (parsed && typeof parsed === "object" && "expression" in parsed) ||
                            // Generic tool parameter format (object with typical tool parameter names)
                            (parsed && typeof parsed === "object" &&
                              (("input" in parsed) || ("prompt" in parsed) || ("text" in parsed) || ("url" in parsed) || ("code" in parsed))
                            ) ||
                            // Array of tool calls
                            (Array.isArray(parsed) && parsed.length > 0 && parsed.every(item =>
                              item && typeof item === "object" && ("name" in item || "function" in item || "tool" in item)
                            ));

                          if (looksLikeTool) {
                            // It's a tool call JSON – skip rendering this part.
                            return null;
                          }
                        }
                      }
                    } catch {
                      /* Parsing failed – fall back to default rendering */
                    }

                    return (
                      <>
                        <MemoizedMarkdown content={debouncedContent} />
                        {isStreaming && (
                          <LoadingDots
                            size="sm"
                            variant="fade"
                            color="primary"
                            className="ml-2"
                          />
                        )}
                      </>
                    );
                  })()}
                </div>
              )}
            </div>
          )}

          {/* Canvas rendering - show the latest version for each unique canvas title */}
          {canvases.length > 0 && (() => {
            // Build a map of title -> latest canvas
            const latestByTitle = new Map<string, CanvasData>();
            canvases.forEach((c) => {
              const existing = latestByTitle.get(c.title);
              if (!existing || existing.updatedAt < c.updatedAt) {
                latestByTitle.set(c.title, c);
              }
            });
            const activeCanvases = Array.from(latestByTitle.values()).sort(
              (a, b) => a.updatedAt - b.updatedAt
            );

            return (
              <div className={cn('mt-4', isAssistant && 'w-full sm:w-4/5')}>
                <div className="text-xs text-muted-foreground mb-1.5">Artifacts</div>
                <div className="flex flex-wrap items-start gap-2">
                   {activeCanvases.map((c) => (
                     <Canvas
                       key={c.updatedAt}
                       messageId={message._id}
                       canvasData={c}
                       isEditable={!isSharedView}
                     />
                   ))}
                 </div>
               </div>
            );
          })()}

          {/* Enhanced Generation Metrics for Assistant Messages - Only show on hover and when enabled */}
          {isAssistant && message.generationMetrics && preferences?.showMessageMetadata && isHovered && (
            <div className="mt-3 pt-3 border-t border-border/20 animate-in fade-in-0 duration-200">
              <div className="space-y-2">
                {/* Primary metrics line */}
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <Database size={12} />
                  <span>{formatMetrics(message.generationMetrics)}</span>
                  {message.isEdited && (
                    <Badge variant="secondary" className="text-xs px-2 py-0.5">
                      Edited
                    </Badge>
                  )}
                </div>

                {/* Additional timing details */}
                {formatDetailedMetrics(message.generationMetrics) && (
                  <div className="text-xs text-muted-foreground/70 pl-5">
                    {formatDetailedMetrics(message.generationMetrics)}
                  </div>
                )}

                {/* Simple context information */}
                {(message.toolCalls?.length || message.thinking) && (
                  <div className="flex items-center gap-3 text-xs text-muted-foreground/70 pl-5">
                    {message.thinking && (
                      <div className="flex items-center gap-1">
                        <Brain size={10} />
                        <span>reasoning</span>
                      </div>
                    )}
                    {message.toolCalls && message.toolCalls.length > 0 && (
                      <div className="flex items-center gap-1">
                        <Wrench size={10} />
                        <span>
                          {groupedToolCalls.length === 1
                            ? groupedToolCalls[0].name.replace(/_/g, ' ')
                            : `${groupedToolCalls.length} tools`
                          }
                        </span>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}
          {message.toolCalls && message.toolCalls.length > 0 && !(message as any).contentSequence && (
            <div className={cn('mt-3 space-y-1', isAssistant && 'w-full sm:w-4/5')}>
              {toolUsageSummary && (
                <div className="text-sm text-muted-foreground pl-1 mb-1">
                  {toolUsageSummary}
                </div>
              )}
              <RowToolCallsGroup toolCalls={message.toolCalls as any} />
            </div>
          )}
        </Card>

        {/* Message Actions and Timestamp */}
        <div className={cn("flex items-center gap-2 px-4 sm:px-6 opacity-30 group-hover:opacity-100 transition-opacity duration-200", isUser ? 'flex-row-reverse' : 'flex-row')}>
          <div className="text-xs text-muted-foreground">
            {new Date(message._creationTime).toLocaleTimeString([], { hour: 'numeric', minute: '2-digit' })}
          </div>
          
          {/* Action Buttons - Hidden in shared view */}
          {!isSharedView && (
            <div className="flex items-center gap-1">
              {/* Copy Button */}
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="h-7 w-7 p-0 hover:bg-muted/70 transition-colors"
                    onClick={handleCopyMessage}
                  >
                    <Copy size={12} />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="top">Copy</TooltipContent>
              </Tooltip>
              
              {/* Edit Button for User Messages */}
              {isUser && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="h-7 w-7 p-0 hover:bg-muted/70 transition-colors"
                      onClick={handleStartEdit}
                    >
                      <Edit2 size={12} />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="top">Edit</TooltipContent>
                </Tooltip>
              )}
              
              {/* Retry Button for Assistant Messages */}
              {isAssistant && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="h-7 w-7 p-0 hover:bg-muted/70 transition-colors"
                      onClick={handleRetryMessage}
                    >
                      <RotateCcw size={12} />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="top">Retry</TooltipContent>
                </Tooltip>
              )}
              
              {/* Branch Off Button - Only for Assistant Messages */}
              {(isAssistant || isUser) && (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-7 w-7 p-0 hover:bg-muted/70 transition-colors"
                        >
                          <GitBranch size={12} />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent 
                        align="end" 
                        avoidCollisions={true}
                        collisionPadding={16}
                        className="w-full min-w-[200px] sm:min-w-[240px] max-h-[60vh] overflow-y-auto bg-background border border-border shadow-lg rounded-xl p-1">
                        <DropdownMenuItem onSelect={() => handleBranchOffWithModel()} className="px-3 py-2 text-sm font-medium hover:bg-muted rounded-md cursor-pointer">
                          Branch off (current model)
                        </DropdownMenuItem>
                        <DropdownMenuSeparator className="my-1" />
                        {Object.entries(getModelsByGroup()).map(([groupName, models]) => (
                          <Collapsible key={groupName} defaultOpen={false}>
                            <DropdownMenuGroup>
                              <CollapsibleTrigger asChild>
                                <div className="flex items-center justify-between cursor-pointer px-3 py-2 text-xs font-semibold text-muted-foreground hover:bg-muted/50 rounded-md">
                                  <span>{groupName}</span>
                                  <ChevronDown size={12} className="transition-transform data-[state=open]:rotate-180" />
                                </div>
                              </CollapsibleTrigger>
                              <CollapsibleContent className="overflow-hidden data-[state=closed]:animate-collapsible-up data-[state=open]:animate-collapsible-down">
                                {models.map((m) => (
                                  <DropdownMenuItem
                                    key={m.id}
                                    onSelect={() => handleBranchOffWithModel(m.provider, m.id)}
                                    className="px-4 py-2 text-sm hover:bg-muted rounded-md cursor-pointer"
                                  >
                                    {m.displayName}{m.parameters ? ` (${m.parameters})` : ''}
                                  </DropdownMenuItem>
                                ))}
                              </CollapsibleContent>
                            </DropdownMenuGroup>
                          </Collapsible>
                        ))}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TooltipTrigger>
                  <TooltipContent side="top">Branch off</TooltipContent>
                </Tooltip>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
});
