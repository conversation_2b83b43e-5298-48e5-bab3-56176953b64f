import { useState, useEffect } from 'react';

/**
 * A generic debouncing hook that delays updating a value until after a specified delay
 * has passed since the last time the value changed. Useful for reducing the frequency
 * of expensive operations like API calls or component rerenders during rapid updates.
 * 
 * @param value - The value to debounce
 * @param delay - The delay in milliseconds to wait before updating the debounced value
 * @returns The debounced value
 */
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    // Set up a timeout to update the debounced value after the delay
    const timeoutId = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    // Cleanup function to clear the timeout if the value changes before the delay
    // This prevents memory leaks and ensures only the latest value is processed
    return () => {
      clearTimeout(timeoutId);
    };
  }, [value, delay]);

  return debouncedValue;
}