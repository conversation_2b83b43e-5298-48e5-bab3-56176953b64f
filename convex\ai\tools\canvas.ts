import { tool } from "ai";
import { z } from "zod";
import { jsonSchema } from "ai";

export const canvasTool = tool({
  description: `Create a rich canvas for three distinct purposes:

1. markdown — Long-form writing (essays, docs, knowledge bases). **Markdown is strictly for text; DO NOT embed Mermaid diagrams here.**
2. code — Interactive web projects written in HTML/CSS/JS (single-file demos, widgets, prototypes).
3. chart — Programmatic chart creation (e.g. Chart.js, ECharts, D3). Provide executable front-end code; the agent will render the chart inside a self-contained HTML document.

When to use this tool:
• The user asks to draft or heavily edit long documents.
• The user wants a live web preview, UI mock-up, or small app.
• The user needs data visualisation / charts.

Avoid using canvas for:
• Quick Q&A style responses.
• Mermaid diagrams (use a dedicated diagram tool instead).
• Tiny illustrative code snippets that don't need a live preview.`,

  inputSchema: jsonSchema({
    type: "object",
    properties: {
      type: {
        type: "string",
        enum: ["markdown", "code", "chart", "react"],
        description:
          "Canvas mode: 'markdown', 'code', 'chart', or 'react' (interactive React preview). For 'react' canvases, the code MUST define a component named 'App' (e.g., 'function App() {...}'). Do NOT use 'export default'.",
      },
      title: {
        type: "string",
        description: "Short, descriptive title for the canvas content",
      },
      content: {
        type: "string",
        description:
          "Main body: markdown text, or full HTML/CSS/JS when type ≠ 'markdown'. For markdown canvases, provide the markdown content. For code canvases, provide HTML/CSS/JS. For chart canvases, you can leave this empty if providing chartSpec.",
      },
      language: {
        type: "string",
        description:
          "Programming language for 'code' canvas (html, css, js, etc.). Leave empty for markdown or chart canvases.",
      },
      chartSpec: {
        type: "string",
        description:
          "For chart canvases only: JSON or JS snippet describing the chart configuration. Leave empty for non-chart canvases.",
      },
      library: {
        type: "string",
        description:
          "For chart canvases only: Preferred JS charting library ('chartjs', 'echarts', or 'd3'). Leave empty for non-chart canvases.",
      },
    },
    required: ["type", "title", "content", "language", "chartSpec", "library"],
    additionalProperties: false,
  }),

  execute: async (args: any) => {
    try {
      const { type, title } = args;
      let { content, language = "", chartSpec = "", library = "" } = args;
      if (!type) {
        return "❌ **Canvas Error: Type Required**\n\nThe canvas tool requires a 'type' parameter. Please specify one of:\n- 'markdown' for text content\n- 'code' for code snippets\n- 'chart' for data visualizations\n- 'react' for interactive components";
      }

      // Disallow mermaid diagrams – use dedicated diagram tool instead
      if (content && /```\s*mermaid/.test(content)) {
        return "❌ **Canvas Error: Mermaid Not Supported**\n\nMermaid diagrams are not supported in canvas. Please use the diagram tool instead or provide the content in a different format.";
      }

      // Handle defaults
      if (!content) {
        content = "";
      }
      if (!language) {
        language = "";
      }
      if (!chartSpec) {
        chartSpec = "";
      }
      if (!library) {
        library = "";
      }

      const finalContent = content || "";
      let finalLanguage = language || ""; // use 'let' to allow mutations for specific types
      const finalChartSpec = chartSpec || "";

      // Handle library parameter - convert to proper enum value or null
      let finalLibrary: "chartjs" | "echarts" | "d3" | null = null;
      if (library && ["chartjs", "echarts", "d3"].includes(library)) {
        finalLibrary = library as "chartjs" | "echarts" | "d3";
      } else if (type === "chart") {
        finalLibrary = "chartjs"; // Default for chart type
      }

      if (type === "chart") {
        if (!finalChartSpec) {
          return "❌ **Canvas Error: Chart Specification Required**\n\nWhen creating a chart, you must provide a 'chartSpec' parameter with the chart configuration.\n\nExample: chartSpec should contain Chart.js configuration object.";
        }
        // The tool is only responsible for providing the chartSpec.
        // The frontend will be responsible for rendering it.
        // The old logic that generated full HTML here has been removed.
      }

      // Support for React preview – compiles TSX/JSX on-the-fly using Babel Standalone
      if (type === "react") {
        if (!finalContent) {
          return "❌ **Canvas Error: React Content Required**\n\nWhen creating a React component, you must provide the component code in the 'content' parameter.\n\nExample: content should contain valid JSX/TSX code.";
        }

        finalLanguage = "tsx";
        // The raw TSX is stored in 'content'. The frontend is responsible for wrapping it
        // in a runnable HTML document for preview. The old wrapping logic is removed.
      }

      return {
        type,
        title,
        content: finalContent || "",
        language: finalLanguage || undefined,
        chartSpec: finalChartSpec || undefined,
        library: finalLibrary || undefined,
        updatedAt: Date.now(),
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      console.error("Canvas tool error:", error);
      return `❌ **Canvas Tool Error**\n\nThe canvas tool encountered an error: ${errorMessage}\n\nPlease check your parameters and try again.`;
    }
  },
});
