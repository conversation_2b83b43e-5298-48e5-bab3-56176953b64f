import { useState, useEffect } from "react";
import { useQuery, useMutation } from "convex/react";
import { toast } from "sonner";
import { api } from "../../../convex/_generated/api";
import { Id } from "../../../convex/_generated/dataModel";
import { Upload, Download, Trash2 } from "lucide-react";
import {
  SettingsCard,
  SettingsCardContent,
  SettingsCardDescription,
  SettingsCardHeader,
  SettingsCardTitle,
} from "@/components/settings/SettingsCard";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import {
  Dialog,
  DialogContent,
  DialogD<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON><PERSON>rigger,
} from "@/components/ui/dialog";

export function DataSection() {
  const instructionsText = useQuery(api.userInstructions.get);
  const updateInstructions = useMutation(api.userInstructions.update);
  const memories = useQuery(api.userMemories.list);
  const deleteMemory = useMutation(api.userMemories.remove);
  const deleteAllMemories = useMutation(api.userMemories.removeAll);
  const deleteAllConversations = useMutation(api.userAccount.deleteAllConversations);
  
  // Import/Export functionality
  const importConversation = useMutation(api.conversations.importConversation);
  const exportAllConversations = useQuery(api.conversations.exportAllConversations);

  const [currentInstructions, setCurrentInstructions] = useState("");
  const [importFile, setImportFile] = useState<File | null>(null);
  const [isImporting, setIsImporting] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [isConfirmDeleteOpen, setIsConfirmDeleteOpen] = useState(false);

  useEffect(() => {
    if (instructionsText !== undefined) {
      setCurrentInstructions(instructionsText);
    }
  }, [instructionsText]);

  const handleSaveInstructions = async () => {
    try {
      await updateInstructions({ instructions: currentInstructions });
      toast.success("Custom instructions saved.");
    } catch (error) {
      toast.error("Failed to save instructions.");
      console.error(error);
    }
  };

  const handleDeleteMemory = async (memoryId: Id<"userMemories">) => {
    try {
      await deleteMemory({ id: memoryId });
      toast.success("Memory deleted.");
    } catch (error) {
      toast.error("Failed to delete memory.");
      console.error(error);
    }
  };

  const handleDeleteAllMemories = async () => {
    try {
      await deleteAllMemories();
      toast.success("All memories deleted.");
    } catch (error) {
      toast.error("Failed to delete memories.");
      console.error(error);
    }
  };

  const handleDeleteAllConversations = async () => {
    try {
      await deleteAllConversations();
      toast.success("All conversations have been deleted.");
      setIsConfirmDeleteOpen(false);
    } catch (error) {
      toast.error("Failed to delete conversations.");
      console.error(error);
    }
  };

  const handleImportConversations = async () => {
    if (!importFile) return;
    
    setIsImporting(true);
    try {
      const text = await importFile.text();
      const importData = JSON.parse(text);
      const result = await importConversation({ exportData: importData });
      
      setImportFile(null);
      
      if (typeof result === 'object' && result !== null && 'importedConversations' in result) {
        toast.success(`Successfully imported ${result.importedConversations} conversations!`);
      } else {
        toast.success("Successfully imported conversation!");
      }
    } catch (error: any) {
      console.error("Failed to import conversation:", error);
      toast.error(`Failed to import: ${error.message || 'Invalid file format'}`);
    } finally {
      setIsImporting(false);
    }
  };

  const handleExportConversations = async () => {
    if (!exportAllConversations) return;
    
    setIsExporting(true);
    try {
      const blob = new Blob([JSON.stringify(exportAllConversations, null, 2)], {
        type: 'application/json',
      });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `all-conversations-export-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      toast.success("Conversations exported successfully!");
    } catch (error) {
      console.error("Failed to export conversations:", error);
      toast.error("Failed to export conversations.");
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <div className="space-y-6">
      <SettingsCard>
        <SettingsCardHeader>
          <SettingsCardTitle className="text-xl font-semibold">Custom Instructions</SettingsCardTitle>
          <SettingsCardDescription>
            Provide custom instructions for the AI to follow in every conversation.
          </SettingsCardDescription>
        </SettingsCardHeader>
        <SettingsCardContent>
          <Textarea
            value={currentInstructions}
            onChange={(e) => setCurrentInstructions(e.target.value)}
            rows={8}
            placeholder="e.g., I am a software engineer working in Python and TypeScript..."
            className="min-h-[120px] resize-vertical"
          />
          <Button onClick={() => {
            void handleSaveInstructions();
          }} className="w-full sm:w-auto">
            Save Instructions
          </Button>
        </SettingsCardContent>
      </SettingsCard>

      {/* Delete Conversations Card */}
      <SettingsCard>
        <SettingsCardHeader>
          <SettingsCardTitle className="text-xl font-semibold">Data Removal</SettingsCardTitle>
          <SettingsCardDescription>
            Permanently delete your data from our servers.
          </SettingsCardDescription>
        </SettingsCardHeader>
        <SettingsCardContent>
          <div className="relative group">
            {/* Subtle glow effect */}
            <div className="absolute -inset-1 bg-gradient-to-r from-red-600/0 via-red-500/5 to-red-600/0 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm" />
            
            {/* Main content */}
            <div className="relative space-y-4">
              <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 p-4 rounded-xl bg-gradient-to-r from-zinc-900/40 to-zinc-800/20 border border-zinc-800/40 group-hover:border-red-700/30 transition-all duration-300">
                <div className="flex items-start gap-3">
                  <div className="p-2 bg-gradient-to-br from-red-900/60 to-red-800/40 rounded-lg border border-red-700/40">
                    <Trash2 className="h-5 w-5 text-red-300" />
                  </div>
                  <div>
                    <h4 className="font-medium text-lg text-zinc-200 mb-1">Delete All Conversations</h4>
                    <p className="text-sm text-zinc-400">
                      Permanently delete all your conversations - this cannot be undone
                    </p>
                  </div>
                </div>
                <Dialog open={isConfirmDeleteOpen} onOpenChange={setIsConfirmDeleteOpen}>
                  <DialogTrigger asChild>
                    <Button 
                      variant="destructive" 
                      className="w-full sm:w-auto bg-gradient-to-r from-red-900/60 to-red-800/40 hover:from-red-800/70 hover:to-red-700/50 border border-red-700/50 hover:border-red-600/70"
                    >
                      <Trash2 className="w-4 h-4 mr-2" />
                      Delete All
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-md bg-gradient-to-br from-zinc-900/95 to-slate-900/90 border border-zinc-700/50 backdrop-blur-lg">
                    <DialogHeader>
                      <DialogTitle className="text-white">Are you absolutely sure?</DialogTitle>
                      <DialogDescription className="text-zinc-400">
                        This action cannot be undone. This will permanently delete
                        all your conversations and remove your data from our
                        servers.
                      </DialogDescription>
                    </DialogHeader>
                    <DialogFooter className="flex flex-col sm:flex-row gap-2 justify-end">
                      <Button
                        variant="outline"
                        onClick={() => setIsConfirmDeleteOpen(false)}
                        className="w-full sm:w-auto bg-gradient-to-r from-zinc-900/40 to-zinc-800/20 border-zinc-700/50 text-zinc-300 hover:border-zinc-600/70 hover:text-white"
                      >
                        Cancel
                      </Button>
                      <Button
                        variant="destructive"
                        onClick={() => {
                          void handleDeleteAllConversations();
                        }}
                        className="w-full sm:w-auto bg-gradient-to-r from-red-900/60 to-red-800/40 hover:from-red-800/70 hover:to-red-700/50"
                      >
                        Yes, Delete All
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
              <Separator className="bg-zinc-800/50" />
            </div>
          </div>
        </SettingsCardContent>
      </SettingsCard>

      <SettingsCard>
        <SettingsCardHeader>
          <SettingsCardTitle className="text-xl font-semibold">Import & Export Conversations</SettingsCardTitle>
          <SettingsCardDescription>
            Import a previously exported conversation JSON file or export all your conversations for backup.
          </SettingsCardDescription>
        </SettingsCardHeader>
        <SettingsCardContent>
          <div className="space-y-2">
            <Label htmlFor="import-file">Select File</Label>
            <Input
              id="import-file"
              type="file"
              accept=".json"
              onChange={(e) => setImportFile(e.target.files?.[0] || null)}
              disabled={isImporting}
              className="file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-primary-foreground hover:file:bg-primary/90"
            />
          </div>
          
          {importFile && (
            <Alert className="shadow-sm">
              <AlertTitle>File Ready to Import</AlertTitle>
              <AlertDescription>
                <p>Selected file: {importFile.name}</p>
                <p className="text-sm text-muted-foreground">{(importFile.size / 1024).toFixed(1)} KB</p>
              </AlertDescription>
            </Alert>
          )}

          {exportAllConversations && (
            <Alert className="shadow-sm">
              <AlertTitle>Ready to Export</AlertTitle>
              <AlertDescription>
                Found <strong>{exportAllConversations.totalConversations}</strong> conversations to export.
              </AlertDescription>
            </Alert>
          )}

          <Separator className="my-4" />
          <div className="flex flex-col sm:flex-row gap-4">
            <Button
              onClick={() => void handleImportConversations()}
              disabled={!importFile || isImporting}
              variant="outline"
              className="w-full sm:w-auto text-sm py-2 px-4 hover:shadow-md transition-shadow"
            >
              {isImporting ? (
                "Importing..."
              ) : (
                <>
                  <Upload className="w-4 h-4 mr-2" />
                  Import
                </>
              )}
            </Button>

            <Button
              onClick={() => void handleExportConversations()}
              disabled={!exportAllConversations || isExporting}
              variant="outline"
              className="w-full sm:w-auto text-sm py-2 px-4 hover:shadow-md transition-shadow"
            >
              {isExporting ? (
                "Exporting..."
              ) : (
                <>
                  <Download className="w-4 h-4 mr-2" />
                  Export All
                </>
              )}
            </Button>
          </div>
        </SettingsCardContent>
      </SettingsCard>

      <SettingsCard>
        <SettingsCardHeader>
          <SettingsCardTitle className="text-xl font-semibold">AI Memory</SettingsCardTitle>
          <SettingsCardDescription>
            Manage the AI's memories from your conversations.
          </SettingsCardDescription>
        </SettingsCardHeader>
        <SettingsCardContent>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <span className="text-sm text-muted-foreground">
              {memories?.length || 0} memories stored
            </span>
            {memories && memories.length > 0 && (
              <Button
                variant="destructive"
                size="sm"
                onClick={() => void handleDeleteAllMemories()}
                className="w-full sm:w-auto"
              >
                <Trash2 className="w-4 h-4 mr-2" />
                Delete All
              </Button>
            )}
          </div>

          {memories && memories.length > 0 ? (
            <div className="space-y-2 max-h-96 overflow-y-auto pr-2">
              {memories.map((memory) => (
                <div
                  key={memory._id}
                  className="p-3 bg-gray-800/50 rounded-lg border border-gray-700/50 flex justify-between items-start"
                >
                  <div className="space-y-1">
                    <p className="text-sm text-gray-400 line-clamp-2">{memory.memory}</p>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => void handleDeleteMemory(memory._id)}
                    className="text-gray-400 hover:text-gray-200"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-muted-foreground text-sm py-4 text-center">
              No memories stored yet. The AI will create memories as you chat.
            </p>
          )}
        </SettingsCardContent>
      </SettingsCard>
    </div>
  );
} 
