"use node";

import { action } from "./_generated/server";
import { v } from "convex/values";

// We'll call the OpenAI moderation endpoint directly to avoid adding the SDK

const OPENAI_API_KEY =
  process.env.CONVEX_OPENAI_API_KEY ?? process.env.OPENAI_API_KEY ?? "";

// OpenAI REST endpoint for moderation
const OPENAI_MODERATION_URL = "https://api.openai.com/v1/moderations";

// Helper to pause execution
const sleep = (ms: number) => new Promise((r) => setTimeout(r, ms));

/**
 * Content moderation using OpenAI's omni-moderation-latest model.
 * Returns true if the provided text is flagged by the moderation model.
 */
export const moderate = action({
  args: {
    text: v.string(),
  },
  returns: v.boolean(),
  handler: async (_ctx, args) => {
    if (!OPENAI_API_KEY) {
      console.warn("OpenAI API key missing. Skipping moderation check.");
      return false; // Fail-open
    }

    const payload = {
      model: "omni-moderation-latest",
      input: args.text,
    };

    const maxAttempts = 3;
    let attempt = 0;
    while (attempt < maxAttempts) {
      try {
        const res = await fetch(OPENAI_MODERATION_URL, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${OPENAI_API_KEY}`,
          },
          body: JSON.stringify(payload),
        });

        if (res.ok) {
          const data: any = await res.json();
          const flagged = data?.results?.[0]?.flagged ?? false;
          return flagged;
        }

        // If we hit rate limit or server error, retry with backoff
        if (res.status === 429 || res.status >= 500) {
          const wait = 500 * Math.pow(2, attempt); // 0.5s, 1s, 2s
          await sleep(wait);
          attempt += 1;
          continue;
        }

        return false;
      } catch (err) {
        // Network or other unexpected error
        if (attempt < maxAttempts - 1) {
          const wait = 500 * Math.pow(2, attempt);
          await sleep(wait);
          attempt += 1;
          continue;
        }
        return false; // Fail-open after retries
      }
    }

    // If all retries fail, allow the message
    return false;
  },
});
