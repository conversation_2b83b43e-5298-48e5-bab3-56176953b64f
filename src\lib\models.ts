// c:/Users/<USER>/Desktop/Erzen/Programing/2025/Main/OpenErzenAI/erzen-ai/src/lib/models.ts

import { fa } from "zod/v4/locales";

export interface ProviderEntry {
  provider: string;
  modelId: string;
  pricing?: {
    input: number;
    output: number;
  };
  priority: number;
}

export interface ModelInfo {
  id: string;
  displayName: string;
  /** @deprecated use modelProviders and groupName instead */
  provider: string;
  groupName?: string;
  modelProviders?: ProviderEntry[];
  maxInputTokens: number;
  maxOutputTokens: number;
  contextWindow: number;
  pricing?: {
    input: number; // per 1M tokens
    output: number; // per 1M tokens
  };
  capabilities: string[];
  description: string;
  icon: string;
  supportsTools: boolean; // Whether the model supports function calling/tools
  isMultimodal: boolean; // Whether the model supports vision/images
  supportsThinking: boolean; // Whether the model supports thinking/reasoning
  thinkingBudgets?: string[] | number[]; // Thinking budget options - can be ['low', 'medium', 'high'] or [1024, 2048, 4096] tokens
  parameters?: string; // Model size/parameters info (e.g., "70B", "Mini", "Lite")
  hidden?: boolean; // Whether the model is hidden from the UI
}

export interface ProviderConfig {
  name: string;
  icon: string;
  models: string[];
  isBuiltIn?: boolean;
}

export const PROVIDER_CONFIGS: Record<string, ProviderConfig> = {
  openai: {
    name: "OpenAIProvider",
    models: [
      "gpt-5",
      "gpt-5-mini",
      "gpt-5-nano",
      "o3-mini",
      "o4-mini",
      "o3",
      "o1",
      "gpt-4.1",
      "gpt-4.1-mini",
      "gpt-4.1-nano",
      "gpt-4o",
      "gpt-4o-mini",
      "o4-mini-deep-research",
      "o3-deep-research",
      "omni-moderation-latest",
      "codex-mini-latest",
      "gpt-oss-120b",
      "gpt-oss-20b",
    ],
    icon: "🤖",
    isBuiltIn: true,
  },
  google: {
    name: "GoogleProvider",
    models: [
      "gemini-2.5-pro",
      "gemini-2.5-flash",
      "gemini-2.5-flash-lite",
      "gemini-2.0-flash",
      "gemini-2.0-flash-lite",
      "gemini-1.5-pro",
      "gemini-1.5-flash",
    ],
    icon: "🔍",
    isBuiltIn: true,
  },
  anthropic: {
    name: "AnthropicProvider",
    models: [
      "claude-sonnet-4-0",
      "claude-opus-4-0",
      "claude-3-7-sonnet-latest",
      "claude-3-5-sonnet-latest",
      "claude-3-5-haiku-latest",
      "claude-3-opus-latest",
    ],
    icon: "🧠",
    isBuiltIn: true,
  },
  openrouter: {
    name: "OpenRouterProvider",
    models: [
      "deepseek/deepseek-chat-v3-0324:free",
      "deepseek/deepseek-r1-0528:free",
      "deepseek/deepseek-r1:free",
      "tngtech/deepseek-r1t-chimera:free",
      "deepseek/deepseek-prover-v2:free",
      "microsoft/phi-4-reasoning-plus:free",
      "mistralai/devstral-small:free",
      "qwen/qwen2.5-vl-72b-instruct:free",
      "mistralai/mistral-small-3.1-24b-instruct:free",
      "google/gemma-3-27b-it:free",
      "qwen/qwen3-30b-a3b:free",
      "anthropic/claude-3.5-sonnet",
      "openai/gpt-4o",
      "meta-llama/llama-3.1-405b-instruct",
      "mistralai/mixtral-8x7b-instruct",
      "cohere/command-r-plus",
      "moonshotai/kimi-dev-72b:free",
      "tngtech/deepseek-r1t2-chimera:free",
    ],
    icon: "🔀",
    isBuiltIn: true,
  },
  groq: {
    name: "GroqProvider",
    models: [
      "moonshotai/kimi-k2-instruct",
      "deepseek-r1-distill-llama-70b",
      "llama-3.3-70b-versatile",
      "qwen-qwq-32b",
      "qwen/qwen3-32b",
      "meta-llama/llama-4-scout-17b-16e-instruct",
      "meta-llama/llama-4-maverick-17b-128e-instruct",
      "compound-beta",
      "compound-beta-mini",
      "llama-3.1-8b-instant",
      "openai/gpt-oss-120b",
      "openai/gpt-oss-20b",
    ],
    icon: "⚡",
    isBuiltIn: true,
  },
  deepseek: {
    name: "DeepSeekProvider",
    models: ["deepseek-chat", "deepseek-reasoner"],
    icon: "🔍",
    isBuiltIn: false,
  },
  grok: {
    name: "GrokProvider",
    models: [
      "grok-3-beta",
      "grok-3-mini-beta",
      "grok-2-vision-1212",
      "grok-2-image-1212",
      "grok-beta",
      "grok-vision-beta",
    ],
    icon: "🚀",
    isBuiltIn: false,
  },
  cohere: {
    name: "CohereProvider",
    models: [
      "command-a-03-2025",
      "command-r7b-12-2024",
      "command-r-plus",
      "command-r-08-2024",
      "command-r",
      "command",
      "c4ai-aya-expanse-32b",
      "c4ai-aya-expanse-8b",
      "c4ai-aya-vision-32b",
      "c4ai-aya-vision-8b",
    ],
    icon: "🎯",
    isBuiltIn: true,
  },
  mistral: {
    name: "MistralProvider",
    models: [
      "magistral-medium-2506",
      "magistral-small-2506",
      "mistral-medium-2505",
      "mistral-small-2503",
      "devstral-small-2505",
      "pixtral-12b-2409",
      "ministral-8b-2410",
      "ministral-3b-2410",
      "mistral-saba-2502",
      "accounts/fireworks/models/mistral-small-24b-instruct-2501",
      "mistral-large-latest",
      "mistral-medium-latest",
      "mistral-small-latest",
      "codestral-latest",
    ],
    icon: "🌟",
    isBuiltIn: false,
  },
  cerebras: {
    name: "CerebrasProvider",
    models: [
      "llama-4-scout-17b-16e-instruct",
      "llama3.1-8b",
      "llama-3.3-70b",
      "qwen-3-32b",
      "deepseek-r1-distill-llama-70b-cerebras",
      "gpt-oss-120b",
      "gpt-oss-20b",
    ],
    icon: "💠",
    isBuiltIn: true,
  },
  github: {
    name: "GitHubProvider",
    models: [
      "openai/gpt-4.1",
      "openai/o3",
      "openai/o1",
      "openai/o4-mini",
      "openai/o3-mini",
      "openai/o1-mini",
      "xai/grok-3",
      "xai/grok-3-mini",
      "deepseek/DeepSeek-R1-0528",
    ],
    icon: "🐙",
    isBuiltIn: true,
  },
  system: {
    name: "System",
    models: ["auto"],
    icon: "⚙️",
    isBuiltIn: true,
  },
};

export const MODEL_INFO: Record<string, ModelInfo> = {
  // OpenAI Models
  "gpt-5": {
    id: "gpt-5",
    displayName: "GPT 5",
    provider: "openai", // kept for backwards compatibility
    groupName: "OpenAI",
    modelProviders: [
      { provider: "openai", modelId: "gpt-5", priority: 1 },
      { provider: "openrouter", modelId: "openai/gpt-5", priority: 2 },
      { provider: "github", modelId: "openai/gpt-5", priority: 3 },
    ],
    maxInputTokens: 256000,
    maxOutputTokens: 256000,
    contextWindow: 256000,
    pricing: { input: 1.1, output: 4.4 },
    capabilities: ["Text", "Code", "Reasoning", "Math"],
    description:
      "Latest OpenAI model with advanced reasoning capabilities",
    icon: "🤖",
    supportsTools: true,
    isMultimodal: true,
    supportsThinking: true,
    thinkingBudgets: ["low", "medium", "high"],
  },
  "gpt-5-mini": {
    id: "gpt-5-mini",
    displayName: "GPT 5",
    provider: "openai", // kept for backwards compatibility
    groupName: "OpenAI",
    modelProviders: [
      { provider: "openai", modelId: "gpt-5-mini", priority: 1 },
      { provider: "openrouter", modelId: "openai/gpt-5-mini", priority: 2 },
      { provider: "github", modelId: "openai/gpt-5-mini", priority: 3 },
    ],
    maxInputTokens: 256000,
    maxOutputTokens: 256000,
    contextWindow: 256000,
    pricing: { input: 1.1, output: 4.4 },
    capabilities: ["Text", "Code", "Reasoning", "Math"],
    description:
      "Mini version of GPT 5 with advanced reasoning capabilities",
    icon: "🤖",
    supportsTools: true,
    isMultimodal: true,
    supportsThinking: true,
    thinkingBudgets: ["low", "medium", "high"],
    parameters: "Mini",
  },
  "gpt-5-nano": {
    id: "gpt-5-nano",
    displayName: "GPT 5",
    provider: "openai",
    groupName: "OpenAI",
    modelProviders: [
      { provider: "openai", modelId: "gpt-5-nano", priority: 1 },
      { provider: "openrouter", modelId: "openai/gpt-5-nano", priority: 2 },
      { provider: "github", modelId: "openai/gpt-5-nano", priority: 3 },
    ],
    maxInputTokens: 256000,
    maxOutputTokens: 256000,
    contextWindow: 256000,
    pricing: { input: 1.1, output: 4.4 },
    capabilities: ["Text", "Code", "Reasoning", "Math"],
    description:
      "Nano version of GPT 5 with advanced capabilities",
    icon: "🤖",
    supportsTools: true,
    isMultimodal: true,
    supportsThinking: false,
    thinkingBudgets: undefined,
    parameters: "Nano",
  },
  "o3-mini": {
    id: "o3-mini",
    displayName: "o3 mini",
    provider: "openai", // kept for backwards compatibility
    groupName: "OpenAI",
    modelProviders: [
      { provider: "openai", modelId: "o3-mini", priority: 1 },
      { provider: "openrouter", modelId: "openai/o3-mini", priority: 2 },
      { provider: "github", modelId: "openai/o3-mini", priority: 3 },
    ],
    maxInputTokens: 200000,
    maxOutputTokens: 100000,
    contextWindow: 200000,
    pricing: { input: 1.1, output: 4.4 },
    capabilities: ["Text", "Code", "Reasoning", "Math"],
    description:
      "Advanced reasoning model optimized for complex problem solving",
    icon: "🤖",
    supportsTools: true,
    isMultimodal: true,
    supportsThinking: true,
    thinkingBudgets: ["low", "medium", "high"],
  },
  "o4-mini": {
    id: "o4-mini",
    displayName: "o4 mini",
    provider: "openai",
    groupName: "OpenAI",
    modelProviders: [
      { provider: "openai", modelId: "o4-mini", priority: 1 },
      { provider: "openrouter", modelId: "openai/o4-mini", priority: 2 },
      { provider: "github", modelId: "openai/o4-mini", priority: 3 },
    ],
    maxInputTokens: 200000,
    maxOutputTokens: 100000,
    contextWindow: 200000,
    pricing: { input: 1.1, output: 4.4 },
    capabilities: ["Text", "Code", "Advanced Reasoning", "Math", "Science"],
    description: "Next-generation reasoning model with enhanced capabilities",
    icon: "🤖",
    supportsTools: true,
    isMultimodal: true,
    supportsThinking: true,
    thinkingBudgets: ["low", "medium", "high"],
  },
  o3: {
    id: "o3",
    displayName: "o3",
    provider: "openai",
    groupName: "OpenAI",
    modelProviders: [
      { provider: "openai", modelId: "o3", priority: 1 },
      { provider: "openrouter", modelId: "openai/o3", priority: 2 },
      { provider: "github", modelId: "openai/o3", priority: 3 },
    ],
    maxInputTokens: 200000,
    maxOutputTokens: 100000,
    contextWindow: 200000,
    pricing: { input: 2, output: 8 },
    capabilities: ["Text", "Code", "Reasoning", "Math"],
    description: "Next-generation reasoning model with enhanced capabilities",
    icon: "🤖",
    supportsTools: true,
    isMultimodal: true,
    supportsThinking: true,
    thinkingBudgets: ["low", "medium", "high"],
  },
  "o3-pro": {
    id: "o3-pro",
    displayName: "o3 pro",
    provider: "openai",
    groupName: "OpenAI",
    modelProviders: [
      { provider: "openai", modelId: "o3-pro", priority: 1 },
      { provider: "openrouter", modelId: "openai/o3-pro", priority: 2 },
      { provider: "github", modelId: "openai/o3-pro", priority: 3 },
    ],
    maxInputTokens: 200000,
    maxOutputTokens: 100000,
    contextWindow: 200000,
    pricing: { input: 20, output: 80 },
    capabilities: ["Text", "Code", "Reasoning", "Math"],
    description: "Next-generation reasoning model with enhanced capabilities",
    icon: "🤖",
    supportsTools: true,
    isMultimodal: false,
    supportsThinking: true,
    thinkingBudgets: ["low", "medium", "high"],
  },
  o1: {
    id: "o1",
    displayName: "o1",
    provider: "openai",
    groupName: "OpenAI",
    modelProviders: [
      { provider: "openai", modelId: "o1", priority: 1 },
      { provider: "openrouter", modelId: "openai/o1", priority: 2 },
      { provider: "github", modelId: "openai/o1", priority: 3 },
    ],
    maxInputTokens: 200000,
    maxOutputTokens: 100000,
    contextWindow: 200000,
    pricing: { input: 15, output: 60 },
    capabilities: ["Text", "Code", "Advanced Reasoning", "Math", "Science"],
    description:
      "Advanced reasoning model optimized for complex problem solving",
    icon: "🤖",
    supportsTools: true,
    isMultimodal: true,
    supportsThinking: true,
    thinkingBudgets: ["low", "medium", "high"],
  },
  "o1-mini": {
    id: "o1-mini",
    displayName: "o1 mini",
    provider: "openai",
    groupName: "OpenAI",
    modelProviders: [
      { provider: "openai", modelId: "o1-mini", priority: 1 },
      { provider: "openrouter", modelId: "openai/o1-mini", priority: 2 },
      { provider: "github", modelId: "openai/o1-mini", priority: 3 },
    ],
    maxInputTokens: 128000,
    maxOutputTokens: 65536,
    contextWindow: 128000,
    pricing: { input: 1.1, output: 4.4 },
    capabilities: ["Text", "Code", "Advanced Reasoning", "Math"],
    description: "Efficient o1 reasoning model",
    icon: "🤖",
    supportsTools: false,
    isMultimodal: false,
    supportsThinking: true,
    thinkingBudgets: ["low", "medium", "high"],
  },
  "gpt-4.1": {
    id: "gpt-4.1",
    displayName: "GPT 4.1",
    provider: "openai",
    groupName: "OpenAI",
    modelProviders: [
      { provider: "openai", modelId: "gpt-4.1", priority: 1 },
      { provider: "openrouter", modelId: "openai/gpt-4.1", priority: 2 },
      { provider: "github", modelId: "openai/gpt-4.1", priority: 3 },
    ],
    maxInputTokens: 1047576,
    maxOutputTokens: 32768,
    contextWindow: 1047576,
    pricing: { input: 2, output: 8 },
    capabilities: ["Text", "Code", "Advanced Reasoning", "Function Calling"],
    description: "Enhanced version of GPT-4 with improved capabilities",
    icon: "🤖",
    supportsTools: true,
    isMultimodal: true,
    supportsThinking: false,
  },
  "gpt-4.1-mini": {
    id: "gpt-4.1-mini",
    displayName: "GPT 4.1",
    provider: "openai",
    groupName: "OpenAI",
    modelProviders: [
      { provider: "openai", modelId: "gpt-4.1-mini", priority: 1 },
      { provider: "openrouter", modelId: "openai/gpt-4.1-mini", priority: 2 },
    ],
    maxInputTokens: 1047576,
    maxOutputTokens: 32768,
    contextWindow: 1047576,
    pricing: { input: 0.4, output: 1.6 },
    capabilities: ["Text", "Code", "Function Calling"],
    description: "Efficient version of GPT 4.1",
    icon: "🤖",
    supportsTools: true,
    isMultimodal: true,
    supportsThinking: false,
    parameters: "Mini",
  },
  "gpt-4.1-nano": {
    id: "gpt-4.1-nano",
    displayName: "GPT 4.1",
    provider: "openai",
    groupName: "OpenAI",
    modelProviders: [
      { provider: "openai", modelId: "gpt-4.1-nano", priority: 1 },
      { provider: "openrouter", modelId: "openai/gpt-4.1-nano", priority: 2 },
    ],
    maxInputTokens: 1047576,
    maxOutputTokens: 32768,
    contextWindow: 1047576,
    pricing: { input: 0.1, output: 0.4 },
    capabilities: ["Text", "Code", "Fast Processing"],
    description: "Ultra-fast and cost-effective nano version of GPT 4.1",
    icon: "🤖",
    supportsTools: true,
    isMultimodal: true,
    supportsThinking: false,
    parameters: "Nano",
  },
  "gpt-4o": {
    id: "gpt-4o",
    displayName: "GPT-4o",
    provider: "openai",
    groupName: "OpenAI",
    modelProviders: [
      { provider: "openai", modelId: "gpt-4o", priority: 1 },
      { provider: "openrouter", modelId: "openai/gpt-4o", priority: 2 },
      { provider: "github", modelId: "openai/gpt-4o", priority: 3 },
    ],
    maxInputTokens: 128000,
    maxOutputTokens: 16384,
    contextWindow: 128000,
    pricing: { input: 5.0, output: 15.0 },
    capabilities: ["Text", "Code", "Vision", "Audio", "Function Calling"],
    description: "Most capable multimodal model with vision and audio",
    icon: "🤖",
    supportsTools: true,
    isMultimodal: true,
    supportsThinking: false,
  },
  "gpt-4o-mini": {
    id: "gpt-4o-mini",
    displayName: "GPT-4o",
    provider: "openai",
    groupName: "OpenAI",
    modelProviders: [
      { provider: "openai", modelId: "gpt-4o-mini", priority: 1 },
      { provider: "openrouter", modelId: "openai/gpt-4o-mini", priority: 2 },
      { provider: "github", modelId: "openai/gpt-4o-mini", priority: 3 },
    ],
    maxInputTokens: 128000,
    maxOutputTokens: 16384,
    contextWindow: 128000,
    pricing: { input: 0.15, output: 0.6 },
    capabilities: ["Text", "Code", "Vision", "Function Calling"],
    description: "Efficient version of GPT-4o with multimodal capabilities",
    icon: "🤖",
    supportsTools: true,
    isMultimodal: true,
    supportsThinking: false,
    parameters: "Mini",
  },
  "o4-mini-deep-research": {
    id: "o4-mini-deep-research",
    displayName: "Fast Research",
    provider: "openai",
    groupName: "OpenAI",
    modelProviders: [
      { provider: "openai", modelId: "o4-mini-deep-research", priority: 1 },
    ],
    maxInputTokens: 200000,
    maxOutputTokens: 100000,
    contextWindow: 200000,
    pricing: { input: 2, output: 8 },
    capabilities: [
      "Text",
      "Code",
      "Deep Research",
      "Advanced Reasoning",
      "Math",
    ],
    description: "Fast research",
    icon: "🤖",
    supportsTools: true,
    isMultimodal: true,
    supportsThinking: false,
    hidden: true,
    thinkingBudgets: ["medium", "high"],
  },
  "o3-deep-research": {
    id: "o3-deep-research",
    displayName: "Deep research",
    provider: "openai",
    groupName: "OpenAI",
    modelProviders: [
      { provider: "openai", modelId: "o3-deep-research", priority: 1 },
    ],
    maxInputTokens: 200000,
    maxOutputTokens: 100000,
    contextWindow: 200000,
    pricing: { input: 10, output: 40 },
    capabilities: [
      "Text",
      "Code",
      "Deep Research",
      "Advanced Reasoning",
      "Math",
    ],
    description: "Deep research",
    icon: "🤖",
    supportsTools: true,
    isMultimodal: true,
    supportsThinking: false,
    hidden: true,
    thinkingBudgets: ["medium", "high"],
  },
  "omni-moderation-latest": {
    id: "omni-moderation-latest",
    displayName: "Moderation",
    provider: "openai",
    groupName: "OpenAI",
    modelProviders: [
      { provider: "openai", modelId: "omni-moderation-latest", priority: 1 },
    ],
    maxInputTokens: 200000,
    maxOutputTokens: 100000,
    contextWindow: 200000,
    pricing: { input: 0, output: 0 },
    capabilities: ["Moderation"],
    description: "Moderation",
    icon: "🤖",
    supportsTools: false,
    isMultimodal: false,
    supportsThinking: false,
    hidden: true,
  },
  "codex-mini-latest": {
    id: "codex-mini-latest",
    displayName: "Codex",
    provider: "openai",
    groupName: "OpenAI",
    modelProviders: [
      { provider: "openai", modelId: "codex-mini-latest", priority: 1 },
    ],
    maxInputTokens: 200000,
    maxOutputTokens: 100000,
    contextWindow: 200000,
    pricing: { input: 1.5, output: 6 },
    capabilities: ["Text", "Code", "Fast Processing"],
    description: "Coding model",
    icon: "🤖",
    supportsTools: true,
    isMultimodal: true,
    supportsThinking: true,
    hidden: true,
  },
  "gpt-oss-120b": {
    id: "gpt-oss-120b",
    displayName: "GPT OSS",
    provider: "openai",
    groupName: "OpenAI",
    modelProviders: [
      { provider: "groq", modelId: "openai/gpt-oss-120b", priority: 1 },
      { provider: "cerebras", modelId: "gpt-oss-120b", priority: 2 },
    ],
    maxInputTokens: 65536,
    maxOutputTokens: 65536,
    contextWindow: 65536,
    pricing: { input: 0.15, output: 0.75 },
    capabilities: ["Text", "Code", "Vision", "Function Calling"],
    description: "SOTA Open Source Model by OpenAI",
    icon: "🤖",
    supportsTools: true,
    isMultimodal: true,
    supportsThinking: true,
    thinkingBudgets: ["low", "medium", "high"],
    parameters: "120B",
  },
  "gpt-oss-20b": {
    id: "gpt-oss-20b",
    displayName: "GPT OSS",
    provider: "openai",
    groupName: "OpenAI",
    modelProviders: [
      { provider: "groq", modelId: "openai/gpt-oss-20b", priority: 1 },
      { provider: "cerebras", modelId: "gpt-oss-20b", priority: 2 },
    ],
    maxInputTokens: 65536,
    maxOutputTokens: 65536,
    contextWindow: 65536,
    pricing: { input: 0.1, output: 0.5 },
    capabilities: ["Text", "Code", "Vision", "Function Calling"],
    description: "SOTA Open Source Model by OpenAI",
    icon: "🤖",
    supportsTools: true,
    isMultimodal: true,
    supportsThinking: true,
    thinkingBudgets: ["low", "medium", "high"],
    parameters: "20B",
  },

  // Google AI Models
  "gemini-2.5-pro": {
    id: "gemini-2.5-pro",
    displayName: "Gemini 2.5 Pro",
    provider: "google",
    groupName: "Google",
    modelProviders: [
      { provider: "google", modelId: "gemini-2.5-pro", priority: 1 },
      { provider: "openrouter", modelId: "google/gemini-2.5-pro", priority: 2 },
    ],
    maxInputTokens: 1048576,
    maxOutputTokens: 65536,
    contextWindow: 1048576,
    pricing: { input: 2.5, output: 10.0 },
    capabilities: [
      "Text",
      "Code",
      "Vision",
      "Audio",
      "Video",
      "Advanced Reasoning",
    ],
    description: "Preview of next-generation Gemini Pro model",
    icon: "🔍",
    supportsTools: true,
    isMultimodal: true,
    supportsThinking: true,
    thinkingBudgets: undefined,
  },
  "gemini-2.5-flash": {
    id: "gemini-2.5-flash",
    displayName: "Gemini 2.5 Flash",
    provider: "google",
    groupName: "Google",
    modelProviders: [
      {
        provider: "google",
        modelId: "gemini-2.5-flash",
        priority: 1,
      },
      {
        provider: "openrouter",
        modelId: "google/gemini-2.5-flash",
        priority: 2,
      },
    ],
    maxInputTokens: 1048576,
    maxOutputTokens: 65536,
    contextWindow: 1048576,
    pricing: { input: 0.3, output: 2.5 },
    capabilities: ["Text", "Code", "Vision", "Audio", "Fast Processing"],
    description: "Preview of next-generation Gemini Flash model",
    icon: "🔍",
    supportsTools: true,
    isMultimodal: true,
    supportsThinking: true,
    thinkingBudgets: [0, 2048, 4096, 8192, 16384, 24576],
  },
  "gemini-2.5-flash-lite": {
    id: "gemini-2.5-flash-lite",
    displayName: "Gemini 2.5 Flash",
    provider: "google",
    groupName: "Google",
    modelProviders: [
      {
        provider: "google",
        modelId: "gemini-2.5-flash-lite",
        priority: 1,
      },
      {
        provider: "openrouter",
        modelId: "google/gemini-2.5-flash-lite",
        priority: 2,
      },
    ],
    maxInputTokens: 1048576,
    maxOutputTokens: 65536,
    contextWindow: 1048576,
    pricing: { input: 0.1, output: 0.4 },
    capabilities: ["Text", "Code", "Vision", "Audio", "Fast Processing"],
    description: "Preview of next-generation Gemini Flash Lite model",
    icon: "🔍",
    supportsTools: true,
    isMultimodal: true,
    supportsThinking: true,
    thinkingBudgets: [0, 2048, 4096, 8192, 16384, 24576],
    parameters: "Lite",
  },
  "gemini-2.0-flash": {
    id: "gemini-2.0-flash",
    displayName: "Gemini 2.0 Flash",
    provider: "google",
    groupName: "Google",
    modelProviders: [
      { provider: "google", modelId: "gemini-2.0-flash", priority: 1 },
      {
        provider: "openrouter",
        modelId: "google/gemini-2.0-flash",
        priority: 2,
      },
    ],
    maxInputTokens: 1000000,
    maxOutputTokens: 8192,
    contextWindow: 1000000,
    pricing: { input: 0.1, output: 0.4 },
    capabilities: ["Text", "Code", "Vision", "Audio", "Video", "Tool Use"],
    description: "Latest multimodal model with massive context window",
    icon: "🔍",
    supportsTools: true,
    isMultimodal: true,
    supportsThinking: false,
  },
  "gemini-2.0-flash-lite": {
    id: "gemini-2.0-flash-lite",
    displayName: "Gemini 2.0 Flash",
    provider: "google",
    groupName: "Google",
    modelProviders: [
      { provider: "google", modelId: "gemini-2.0-flash-lite", priority: 1 },
      {
        provider: "openrouter",
        modelId: "google/gemini-2.0-flash-lite",
        priority: 2,
      },
    ],
    maxInputTokens: 1000000,
    maxOutputTokens: 8192,
    contextWindow: 1000000,
    pricing: { input: 0.075, output: 0.3 },
    capabilities: ["Text", "Code", "Vision", "Fast Processing"],
    description: "Lightweight version of Gemini 2.0 Flash",
    icon: "🔍",
    supportsTools: true,
    isMultimodal: true,
    supportsThinking: false,
    parameters: "Lite",
  },

  // Anthropic Models
  "claude-sonnet-4-0": {
    id: "claude-sonnet-4-0",
    displayName: "Claude Sonnet 4",
    provider: "anthropic",
    groupName: "Anthropic",
    modelProviders: [
      { provider: "anthropic", modelId: "claude-sonnet-4-0", priority: 1 },
      {
        provider: "openrouter",
        modelId: "anthropic/claude-sonnet-4",
        priority: 2,
      },
    ],
    maxInputTokens: 200000,
    maxOutputTokens: 64000,
    contextWindow: 200000,
    pricing: { input: 3.0, output: 15.0 },
    capabilities: ["Text", "Code", "Vision", "Advanced Reasoning", "Research"],
    description: "Next-generation Claude model with enhanced capabilities",
    icon: "🧠",
    supportsTools: true,
    isMultimodal: true,
    supportsThinking: true,
    thinkingBudgets: [2048, 4096, 8192],
  },
  "claude-opus-4-0": {
    id: "claude-opus-4-0",
    displayName: "Claude Opus 4",
    provider: "anthropic",
    groupName: "Anthropic",
    modelProviders: [
      { provider: "anthropic", modelId: "claude-opus-4-0", priority: 1 },
      {
        provider: "openrouter",
        modelId: "anthropic/claude-opus-4",
        priority: 2,
      },
    ],
    maxInputTokens: 200000,
    maxOutputTokens: 32000,
    contextWindow: 200000,
    pricing: { input: 15.0, output: 75.0 },
    capabilities: [
      "Text",
      "Code",
      "Vision",
      "Complex Reasoning",
      "Research",
      "Analysis",
    ],
    description: "Most advanced Claude model for the most challenging tasks",
    icon: "🧠",
    supportsTools: true,
    isMultimodal: true,
    supportsThinking: true,
    thinkingBudgets: [2048, 4096, 8192],
  },
  "claude-3-7-sonnet-latest": {
    id: "claude-3-7-sonnet-latest",
    displayName: "Claude 3.7 Sonnet",
    provider: "anthropic",
    groupName: "Anthropic",
    modelProviders: [
      {
        provider: "anthropic",
        modelId: "claude-3-7-sonnet-latest",
        priority: 1,
      },
      {
        provider: "openrouter",
        modelId: "anthropic/claude-3.7-sonnet",
        priority: 2,
      },
    ],
    maxInputTokens: 200000,
    maxOutputTokens: 64000,
    contextWindow: 200000,
    pricing: { input: 3, output: 15 },
    capabilities: [
      "Text",
      "Code",
      "Vision",
      "Advanced Reasoning",
      "Creative Writing",
    ],
    description: "Enhanced version of Claude 3.5 with improved reasoning",
    icon: "🧠",
    supportsTools: true,
    isMultimodal: true,
    supportsThinking: true,
    thinkingBudgets: [2048, 4096, 8192],
  },
  "claude-3-5-sonnet-latest": {
    id: "claude-3-5-sonnet-latest",
    displayName: "Claude 3.5 Sonnet",
    provider: "anthropic",
    groupName: "Anthropic",
    modelProviders: [
      {
        provider: "anthropic",
        modelId: "claude-3-5-sonnet-latest",
        priority: 1,
      },
      {
        provider: "openrouter",
        modelId: "anthropic/claude-3.5-sonnet",
        priority: 2,
      },
    ],
    maxInputTokens: 200000,
    maxOutputTokens: 8192,
    contextWindow: 200000,
    pricing: { input: 3.0, output: 15.0 },
    capabilities: ["Text", "Code", "Vision", "Analysis", "Creative Writing"],
    description: "Balanced model for complex reasoning and creative tasks",
    icon: "🧠",
    supportsTools: true,
    isMultimodal: true,
    supportsThinking: false,
  },
  "claude-3-5-haiku-latest": {
    id: "claude-3-5-haiku-latest",
    displayName: "Claude 3.5 Haiku",
    provider: "anthropic",
    groupName: "Anthropic",
    modelProviders: [
      {
        provider: "anthropic",
        modelId: "claude-3-5-haiku-latest",
        priority: 1,
      },
      {
        provider: "openrouter",
        modelId: "anthropic/claude-3.5-haiku",
        priority: 2,
      },
    ],
    maxInputTokens: 200000,
    maxOutputTokens: 8192,
    contextWindow: 200000,
    pricing: { input: 0.8, output: 4.0 },
    capabilities: ["Text", "Code", "Fast Processing", "Concise Responses"],
    description: "Fast and efficient model for quick tasks",
    icon: "🧠",
    supportsTools: true,
    isMultimodal: true,
    supportsThinking: false,
  },

  // DeepSeek Models
  "deepseek-chat": {
    id: "deepseek-chat",
    displayName: "DeepSeek V3",
    provider: "deepseek",
    groupName: "DeepSeek",
    modelProviders: [
      { provider: "deepseek", modelId: "deepseek-chat", priority: 1 },
      {
        provider: "openrouter",
        modelId: "deepseek/deepseek-chat-v3-0324:free",
        priority: 2,
      },
      {
        provider: "openrouter",
        modelId: "deepseek/deepseek-chat-v3-0324",
        priority: 3,
      },
    ],
    maxInputTokens: 164000,
    maxOutputTokens: 164000,
    contextWindow: 164000,
    pricing: { input: 0.27, output: 1.1 },
    capabilities: ["Text", "Code", "Math", "Reasoning"],
    description: "Advanced reasoning model from DeepSeek",
    icon: "🔍",
    supportsTools: true,
    isMultimodal: false,
    supportsThinking: false,
  },
  "deepseek-r1-0528": {
    id: "deepseek-r1-0528",
    displayName: "DeepSeek R1",
    provider: "deepseek",
    groupName: "DeepSeek",
    modelProviders: [
      { provider: "deepseek", modelId: "deepseek-reasoner", priority: 3 },
      {
        provider: "openrouter",
        modelId: "deepseek/deepseek-r1-0528:free",
        priority: 1,
      },
      {
        provider: "openrouter",
        modelId: "deepseek/deepseek-r1-0528",
        priority: 2,
      },
    ],
    maxInputTokens: 64000,
    maxOutputTokens: 8192,
    contextWindow: 64000,
    pricing: { input: 0.55, output: 2.19 },
    capabilities: ["Text", "Code", "Advanced Reasoning", "Math"],
    description:
      "DeepSeek R1 reasoning model with advanced thinking capabilities",
    icon: "🔍",
    supportsTools: false,
    isMultimodal: false,
    supportsThinking: true,
    parameters: "0528",
  },
  "deepseek-r1": {
    id: "deepseek-r1",
    displayName: "DeepSeek R1",
    provider: "deepseek",
    groupName: "DeepSeek",
    modelProviders: [
      {
        provider: "openrouter",
        modelId: "deepseek/deepseek-r1:free",
        priority: 1,
      },
      { provider: "openrouter", modelId: "deepseek/deepseek-r1", priority: 2 },
    ],
    maxInputTokens: 128000,
    maxOutputTokens: 8192,
    contextWindow: 128000,
    pricing: { input: 0.55, output: 2.19 },
    capabilities: ["Text", "Code", "Advanced Reasoning", "Math"],
    description: "DeepSeek R1 reasoning model from May 28th",
    icon: "🔍",
    supportsTools: false,
    isMultimodal: false,
    supportsThinking: true,
    thinkingBudgets: undefined,
  },

  // xAI Models (Grok)
  "grok-4": {
    id: "grok-4",
    displayName: "Grok 4",
    provider: "grok",
    groupName: "xAI",
    modelProviders: [
      { provider: "grok", modelId: "grok-4-latest", priority: 1 },
      { provider: "openrouter", modelId: "x-ai/grok-4", priority: 2 },
    ],
    maxInputTokens: 256000,
    maxOutputTokens: 256000,
    contextWindow: 256000,
    pricing: { input: 3.0, output: 15.0 },
    capabilities: [
      "Text",
      "Code",
      "Enterprise Tasks",
      "Programming",
      "Data Extraction",
    ],
    description:
      "Flagship model that excels at enterprise tasks like data extraction, programming, and text summarization",
    icon: "🚀",
    supportsTools: true,
    isMultimodal: true,
    supportsThinking: true,
    thinkingBudgets: undefined,
  },
  "grok-3-beta": {
    id: "grok-3-beta",
    displayName: "Grok 3",
    provider: "grok",
    groupName: "xAI",
    modelProviders: [
      { provider: "grok", modelId: "grok-3-beta", priority: 1 },
      { provider: "openrouter", modelId: "x-ai/grok-3-beta", priority: 2 },
    ],
    maxInputTokens: 131072,
    maxOutputTokens: 131072,
    contextWindow: 131072,
    pricing: { input: 3.0, output: 15.0 },
    capabilities: [
      "Text",
      "Code",
      "Enterprise Tasks",
      "Programming",
      "Data Extraction",
    ],
    description:
      "Flagship model that excels at enterprise tasks like data extraction, programming, and text summarization",
    icon: "🚀",
    supportsTools: true,
    isMultimodal: true,
    supportsThinking: false,
    thinkingBudgets: undefined,
  },
  "grok-3-mini-beta": {
    id: "grok-3-mini-beta",
    displayName: "Grok 3",
    provider: "grok",
    groupName: "xAI",
    modelProviders: [
      { provider: "grok", modelId: "grok-3-mini-beta", priority: 1 },
      { provider: "openrouter", modelId: "x-ai/grok-3-mini-beta", priority: 2 },
    ],
    maxInputTokens: 131072,
    maxOutputTokens: 8192,
    contextWindow: 131072,
    pricing: { input: 0.3, output: 0.5 },
    capabilities: ["Text", "Code", "Math", "Reasoning", "Quantitative Tasks"],
    description:
      "Lightweight model that thinks before responding. Excels at quantitative tasks involving math and reasoning",
    icon: "🚀",
    supportsTools: true,
    isMultimodal: false,
    supportsThinking: true,
    thinkingBudgets: ["low", "high"],
    parameters: "Mini",
  },

  // Cohere Models
  "command-a-03-2025": {
    id: "command-a-03-2025",
    displayName: "Command A",
    provider: "cohere",
    groupName: "Cohere",
    modelProviders: [
      { provider: "cohere", modelId: "command-a-03-2025", priority: 1 },
    ],
    maxInputTokens: 256000,
    maxOutputTokens: 8192,
    contextWindow: 256000,
    pricing: { input: 3.0, output: 15.0 }, // Estimated pricing
    capabilities: ["Text", "Code", "Tool Use", "RAG", "Multilingual", "Agents"],
    description:
      "Most performant model excelling at tool use, agents, RAG, and multilingual use cases",
    icon: "🎯",
    supportsTools: true,
    isMultimodal: false,
    supportsThinking: false,
  },
  "command-r": {
    id: "command-r",
    displayName: "Command R",
    provider: "cohere",
    groupName: "Cohere",
    modelProviders: [{ provider: "cohere", modelId: "command-r", priority: 1 }],
    maxInputTokens: 128000,
    maxOutputTokens: 4096,
    contextWindow: 128000,
    pricing: { input: 1.5, output: 7.5 },
    capabilities: ["Text", "Code", "RAG", "Multilingual"],
    description: "Balanced model for retrieval-augmented generation",
    icon: "🎯",
    supportsTools: true,
    isMultimodal: false,
    supportsThinking: false,
  },
  "c4ai-aya-expanse-32b": {
    id: "c4ai-aya-expanse-32b",
    displayName: "Aya Expanse 32B",
    provider: "cohere",
    groupName: "Cohere",
    modelProviders: [
      { provider: "cohere", modelId: "c4ai-aya-expanse-32b", priority: 1 },
    ],
    maxInputTokens: 128000,
    maxOutputTokens: 4096,
    contextWindow: 128000,
    pricing: { input: 2.0, output: 6.0 }, // Estimated pricing
    capabilities: ["Text", "Code", "Multilingual", "23 Languages"],
    description:
      "Highly performant 32B multilingual model serving 23 languages with monolingual-level performance",
    icon: "🎯",
    supportsTools: true,
    isMultimodal: false,
    supportsThinking: false,
  },

  "c4ai-aya-vision-32b": {
    id: "c4ai-aya-vision-32b",
    displayName: "Aya Vision 32B",
    provider: "cohere",
    groupName: "Cohere",
    modelProviders: [
      { provider: "cohere", modelId: "c4ai-aya-vision-32b", priority: 1 },
    ],
    maxInputTokens: 16384,
    maxOutputTokens: 4096,
    contextWindow: 16384,
    pricing: { input: 2.5, output: 7.5 }, // Estimated pricing
    capabilities: [
      "Text",
      "Code",
      "Vision",
      "Multimodal",
      "Multilingual",
      "23 Languages",
    ],
    description:
      "State-of-the-art 32B multimodal model excelling at language, text, and image capabilities",
    icon: "🎯",
    supportsTools: true,
    isMultimodal: true,
    supportsThinking: false,
  },

  // Mistral AI Models
  "magistral-medium-2506": {
    id: "magistral-medium-2506",
    displayName: "Magistral Medium",
    provider: "mistral",
    groupName: "Mistral",
    modelProviders: [
      { provider: "mistral", modelId: "magistral-medium-2506", priority: 1 },
    ],
    maxInputTokens: 40000,
    maxOutputTokens: 8192,
    contextWindow: 40000,
    pricing: { input: 2.0, output: 5.0 },
    capabilities: [
      "Text",
      "Code",
      "Reasoning",
      "Domain-Specific",
      "Multilingual",
    ],
    description:
      "Frontier-class reasoning model excelling in domain-specific, transparent, and multilingual reasoning",
    icon: "🌟",
    supportsTools: true,
    isMultimodal: false,
    supportsThinking: false,
  },

  "mistral-medium-2505": {
    id: "mistral-medium-2505",
    displayName: "Mistral Medium 3",
    provider: "mistral",
    groupName: "Mistral",
    modelProviders: [
      { provider: "mistral", modelId: "mistral-medium-2505", priority: 1 },
    ],
    maxInputTokens: 128000,
    maxOutputTokens: 8192,
    contextWindow: 128000,
    pricing: { input: 0.4, output: 2.0 },
    capabilities: ["Text", "Code", "Vision", "Multimodal", "Enterprise"],
    description:
      "State-of-the-art performance with simplified enterprise deployments and cost efficiency",
    icon: "🌟",
    supportsTools: true,
    isMultimodal: true,
    supportsThinking: false,
  },

  "mistral-small-24b-instruct-2501": {
    id: "mistral-small-24b-instruct-2501",
    displayName: "Mistral Small 24B",
    provider: "mistral",
    groupName: "Mistral",
    modelProviders: [
      {
        provider: "mistral",
        modelId: "accounts/fireworks/models/mistral-small-24b-instruct-2501",
        priority: 1,
      },
      {
        provider: "openrouter",
        modelId: "mistralai/mistral-small-3.1-24b-instruct:free",
        priority: 2,
      },
    ],
    maxInputTokens: 128000,
    maxOutputTokens: 8192,
    contextWindow: 128000,
    pricing: { input: 0.2, output: 0.6 },
    capabilities: ["Text", "Code", "Fast Processing"],
    description: "Latest small Mistral model for efficient tasks",
    icon: "🌟",
    supportsTools: true,
    isMultimodal: false,
    supportsThinking: false,
  },
  "mistral-large-latest": {
    id: "mistral-large-latest",
    displayName: "Mistral Large",
    provider: "mistral",
    groupName: "Mistral",
    modelProviders: [
      { provider: "mistral", modelId: "mistral-large-latest", priority: 1 },
    ],
    maxInputTokens: 128000,
    maxOutputTokens: 8192,
    contextWindow: 128000,
    pricing: { input: 2.0, output: 6.0 },
    capabilities: ["Text", "Code", "Function Calling", "Reasoning"],
    description: "Flagship model from Mistral AI",
    icon: "🌟",
    supportsTools: true,
    isMultimodal: false,
    supportsThinking: false,
  },
  "mistral-medium-latest": {
    id: "mistral-medium-latest",
    displayName: "Mistral Medium",
    provider: "mistral",
    groupName: "Mistral",
    modelProviders: [
      { provider: "mistral", modelId: "mistral-medium-latest", priority: 1 },
    ],
    maxInputTokens: 128000,
    maxOutputTokens: 8192,
    contextWindow: 128000,
    pricing: { input: 1.0, output: 3.0 },
    capabilities: ["Text", "Code", "Function Calling"],
    description: "Balanced model from Mistral AI",
    icon: "🌟",
    supportsTools: true,
    isMultimodal: false,
    supportsThinking: false,
  },

  // OpenRouter Free Models (categorized by actual model creator)
  "qwen3-coder": {
    id: "qwen3-coder",
    displayName: "Qwen 3",
    provider: "openrouter",
    groupName: "Alibaba",
    modelProviders: [
      {
        provider: "openrouter",
        modelId: "qwen/qwen3-coder:free",
        priority: 1,
      },
      {
        provider: "openrouter",
        modelId: "qwen/qwen3-coder",
        priority: 2,
      },
    ],
    maxInputTokens: 262144,
    maxOutputTokens: 262144,
    contextWindow: 262144,
    pricing: { input: 0.3, output: 1.2 },
    capabilities: ["Text", "Code", "Reasoning"],
    description:
      "A powerful code generation model from Qwen, designed to help with complex coding tasks, tool use, and understanding large codebases. Great for developers who need smart, reliable coding assistance.",
    icon: "🔀",
    supportsTools: true,
    isMultimodal: true,
    supportsThinking: false,
    thinkingBudgets: undefined,
    parameters: "Coder",
  },
  "qwen-3-instruct": {
    id: "qwen-3-instruct",
    displayName: "Qwen 3",
    provider: "openrouter",
    groupName: "Alibaba",
    modelProviders: [
      {
        provider: "openrouter",
        modelId: "qwen/qwen3-235b-a22b-2507:free",
        priority: 1,
      },
      {
        provider: "openrouter",
        modelId: "qwen/qwen3-235b-a22b-2507",
        priority: 2,
      },
    ],
    maxInputTokens: 262144,
    maxOutputTokens: 262144,
    contextWindow: 262144,
    pricing: { input: 0.2, output: 0.6 },
    capabilities: ["Text", "Code", "Advanced Reasoning", "Math"],
    description: "Qwen 3 Instruct model ",
    icon: "🔀",
    supportsTools: true,
    isMultimodal: true,
    supportsThinking: false,
  },

  // Groq Models (categorized by actual model creator)
  "kimi-k2-instruct": {
    id: "kimi-k2-instruct",
    displayName: "Kimi K2",
    provider: "groq",
    groupName: "Moonshot AI",
    modelProviders: [
      { provider: "groq", modelId: "moonshotai/kimi-k2-instruct", priority: 2 },
      {
        provider: "openrouter",
        modelId: "moonshotai/kimi-k2:free",
        priority: 1,
      },
    ],
    maxInputTokens: 128000,
    maxOutputTokens: 16384,
    contextWindow: 128000,
    pricing: { input: 1, output: 3 },
    capabilities: ["Text", "Code", "Ultra-Fast Inference"],
    description:
      "Kimi K2 model is a powerful model for code generation and tool use",
    icon: "⚡",
    supportsTools: true,
    isMultimodal: false,
    supportsThinking: false,
  },
  "deepseek-r1-distill-llama-70b": {
    id: "deepseek-r1-distill-llama-70b",
    displayName: "DeepSeek R1 Llama",
    provider: "groq",
    groupName: "DeepSeek",
    modelProviders: [
      {
        provider: "groq",
        modelId: "deepseek-r1-distill-llama-70b",
        priority: 1,
      },
      {
        provider: "openrouter",
        modelId: "deepseek/deepseek-r1-distill-llama-70b:free",
        priority: 2,
      },
    ],
    maxInputTokens: 128000,
    maxOutputTokens: 8192,
    contextWindow: 128000,
    pricing: { input: 0.75, output: 0.99 },
    capabilities: ["Text", "Code", "Reasoning", "Ultra-Fast Inference"],
    description: "DeepSeek R1 distilled model with reasoning capabilities",
    icon: "⚡",
    supportsTools: true,
    isMultimodal: false,
    supportsThinking: true,
  },
  "llama-3.3-70b": {
    id: "llama-3.3-70b",
    displayName: "Llama 3.3",
    provider: "groq",
    groupName: "Meta",
    modelProviders: [
      { provider: "groq", modelId: "llama-3.3-70b-versatile", priority: 2 },
      {
        provider: "cerebras",
        modelId: "llama-3.3-70b",
        priority: 1,
        pricing: { input: 0.85, output: 1.2 },
      },
    ],
    maxInputTokens: 128000,
    maxOutputTokens: 32768,
    contextWindow: 128000,
    pricing: { input: 0.59, output: 0.79 },
    capabilities: ["Text", "Code", "Ultra-Fast Inference"],
    description: "Latest Llama model with ultra-fast inference on Groq",
    icon: "⚡",
    supportsTools: true,
    isMultimodal: false,
    supportsThinking: false,
  },
  "llama-4-scout-17b-16e-instruct": {
    id: "llama-4-scout-17b-16e-instruct",
    displayName: "Llama 4 Scout",
    provider: "groq",
    groupName: "Meta",
    modelProviders: [
      {
        provider: "groq",
        modelId: "meta-llama/llama-4-scout-17b-16e-instruct",
        priority: 1,
      },
      {
        provider: "cerebras",
        modelId: "llama-4-scout-17b-16e-instruct",
        priority: 2,
      },
    ],
    maxInputTokens: 131072,
    maxOutputTokens: 8192,
    contextWindow: 131072,
    pricing: { input: 0.11, output: 0.34 },
    capabilities: [
      "Text",
      "Code",
      "Vision",
      "Multimodal",
      "Ultra-Fast Inference",
    ],
    description:
      "Multimodal Llama 4 model with 16 experts for text and image understanding",
    icon: "⚡",
    supportsTools: true,
    isMultimodal: true,
    supportsThinking: false,
  },
  "meta-llama/llama-4-maverick-17b-128e-instruct": {
    id: "meta-llama/llama-4-maverick-17b-128e-instruct",
    displayName: "Llama 4 Maverick",
    provider: "groq",
    groupName: "Meta",
    modelProviders: [
      {
        provider: "groq",
        modelId: "meta-llama/llama-4-maverick-17b-128e-instruct",
        priority: 1,
      },
      {
        provider: "cerebras",
        modelId: "llama-4-maverick-17b-128e-instruct",
        priority: 2,
      },
    ],
    maxInputTokens: 131072,
    maxOutputTokens: 8192,
    contextWindow: 131072,
    pricing: { input: 0.2, output: 0.6 },
    capabilities: [
      "Text",
      "Code",
      "Vision",
      "Multimodal",
      "Ultra-Fast Inference",
    ],
    description:
      "Advanced multimodal Llama 4 model with 128 experts for superior performance",
    icon: "⚡",
    supportsTools: true,
    isMultimodal: true,
    supportsThinking: false,
  },
  "compound-beta": {
    id: "compound-beta",
    displayName: "Compound",
    provider: "groq",
    groupName: "Groq",
    modelProviders: [
      { provider: "groq", modelId: "compound-beta", priority: 1 },
    ],
    maxInputTokens: 128000,
    maxOutputTokens: 8192,
    contextWindow: 128000,
    pricing: { input: 0.15, output: 0.45 }, // Estimated based on underlying models
    capabilities: [
      "Text",
      "Code",
      "Web Search",
      "Code Execution",
      "Agentic Tools",
    ],
    description:
      "Compound AI system with web search and code execution capabilities",
    icon: "⚡",
    supportsTools: false,
    isMultimodal: false,
    supportsThinking: true,
  },
  "compound-beta-mini": {
    id: "compound-beta-mini",
    displayName: "Compound ",
    provider: "groq",
    groupName: "Groq",
    modelProviders: [
      { provider: "groq", modelId: "compound-beta-mini", priority: 1 },
    ],
    maxInputTokens: 128000,
    maxOutputTokens: 8192,
    contextWindow: 128000,
    pricing: { input: 0.1, output: 0.3 }, // Estimated based on underlying models
    capabilities: [
      "Text",
      "Code",
      "Web Search",
      "Code Execution",
      "Agentic Tools",
    ],
    description:
      "Efficient version of Compound Beta with web search and code execution",
    icon: "⚡",
    supportsTools: false,
    isMultimodal: false,
    supportsThinking: true,
  },

  "qwen-3-32b": {
    id: "qwen-3-32b",
    displayName: "Qwen 3",
    provider: "cerebras",
    groupName: "Alibaba",
    modelProviders: [
      { provider: "cerebras", modelId: "qwen-3-32b", priority: 1 },
      {
        provider: "groq",
        modelId: "qwen/qwen3-32b",
        priority: 2,
        pricing: { input: 0.29, output: 0.59 },
      },
    ],
    maxInputTokens: 128000,
    maxOutputTokens: 8192,
    contextWindow: 128000,
    pricing: { input: 0.4, output: 0.8 },
    capabilities: ["Text", "Code", "Reasoning", "Ultra-Fast Inference"],
    description: "Qwen 3 32B reasoning model on Cerebras",
    icon: "💠",
    supportsTools: true,
    isMultimodal: false,
    supportsThinking: true,
    parameters: "32B",
  },
  auto: {
    id: "auto",
    displayName: "Best choice",
    provider: "system",
    groupName: "Quad",
    modelProviders: [{ provider: "system", modelId: "auto", priority: 1 }],
    maxInputTokens: Infinity,
    maxOutputTokens: Infinity,
    contextWindow: Infinity,
    capabilities: ["Auto-selection"],
    description:
      "Automatically chooses the best model for your task using AI classification.",
    icon: "⚙️",
    supportsTools: true,
    isMultimodal: true,
    supportsThinking: true,
    hidden: true,
  },
};
/**
 * Get display name for a model
 */
export const getModelDisplayName = (modelId: string): string => {
  const modelInfo = MODEL_INFO?.[modelId];
  if (modelInfo) return modelInfo.displayName;

  // Fallback to heuristic transformation
  let name = modelId.substring(modelId.lastIndexOf("/") + 1);
  const colonIndex = name.indexOf(":");
  if (colonIndex !== -1) name = name.substring(0, colonIndex);

  name = name.replace(/[_-]+/g, " ").replace(/\s+/g, " ").trim();

  const acronyms = new Set([
    "gpt",
    "llama",
    "qwen",
    "gemini",
    "grok",
    "mpt",
    "mixtral",
    "command",
    "cl",
    "ai",
  ]);
  name = name
    .split(" ")
    .map((word) => {
      const lower = word.toLowerCase();
      if (acronyms.has(lower)) return lower.toUpperCase();
      return word.charAt(0).toUpperCase() + word.slice(1);
    })
    .join(" ");

  return name;
};

/**
 * Format token count for display
 */
export const formatTokenCount = (tokens: number): string => {
  if (tokens >= 1000000) {
    return `${(tokens / 1000000).toFixed(1)}M`;
  } else if (tokens >= 1000) {
    return `${(tokens / 1000).toFixed(0)}K`;
  }
  return tokens.toString();
};

/**
 * Format pricing for display
 */
export const formatPricing = (price: number): string => {
  if (price < 1) {
    return price.toFixed(2);
  }
  return price.toFixed(0);
};

/**
 * Generate fallback model info for models not in our detailed database
 */
const generateFallbackModelInfo = (modelId: string): ModelInfo => {
  // Use OpenRouter as fallback since it's a provider aggregator that supports many models
  const provider =
    Object.keys(PROVIDER_CONFIGS).find((p) =>
      PROVIDER_CONFIGS[p].models.includes(modelId)
    ) || "openrouter";

  const providerConfig = PROVIDER_CONFIGS[provider];
  const displayName = getModelDisplayName(modelId);

  // Estimate token limits based on model name patterns
  let maxInputTokens = 32000;
  let maxOutputTokens = 4096;
  let contextWindow = 32000;
  const capabilities = ["Text", "Code"];
  let supportsTools = true; // Default to true for unknown models
  let isMultimodal = false; // Default to false for unknown models
  let supportsThinking = false; // Default to false for unknown models
  let thinkingBudgets: string[] | number[] | undefined = undefined;

  // Heuristics based on model names
  if (
    modelId.includes("vision") ||
    modelId.includes("flash") ||
    provider === "google"
  ) {
    capabilities.push("Vision");
    isMultimodal = true;
    if (provider === "google") {
      maxInputTokens = 1000000;
      contextWindow = 1000000;
    }
  }
  if (modelId.includes("70b") || modelId.includes("405b")) {
    maxInputTokens = 131072;
    contextWindow = 131072;
    maxOutputTokens = 8192;
  }
  if (modelId.includes("claude")) {
    maxInputTokens = 200000;
    contextWindow = 200000;
    maxOutputTokens = 8192;
    capabilities.push("Analysis", "Creative Writing");
    isMultimodal =
      modelId.includes("vision") ||
      modelId.includes("3.5") ||
      modelId.includes("opus");
    // Claude 3.7+ and 4+ support thinking
    if (
      modelId.includes("3.7") ||
      modelId.includes("4") ||
      modelId.includes("claude-4") ||
      modelId.includes("claude-3-7")
    ) {
      supportsThinking = true;
      thinkingBudgets = [1024, 2048, 4096, 8192, 12000, 15000];
    }
  }
  if (modelId.includes("grok")) {
    capabilities.push("Real-time Info", "Humor");
    isMultimodal = modelId.includes("vision");
    supportsThinking = true;
    thinkingBudgets = [1024, 2048, 4096];
  }
  if (modelId.includes("coder") || modelId.includes("codestral")) {
    capabilities.push("Programming", "Debugging");
  }
  if (
    modelId.includes("reasoning") ||
    modelId.includes("r1") ||
    modelId.includes("qwq") ||
    modelId.includes("o3") ||
    modelId.includes("o4") ||
    modelId.includes("o1")
  ) {
    capabilities.push("Advanced Reasoning", "Math");
    supportsTools = false; // Reasoning models typically don't support tools
    supportsThinking = true;
    if (
      modelId.includes("o1") ||
      modelId.includes("o3") ||
      modelId.includes("o4")
    ) {
      thinkingBudgets = ["low", "medium", "high"]; // OpenAI uses reasoning effort
    } else {
      thinkingBudgets = [1024, 2048, 4096]; // Other reasoning models use token budgets
    }
  }
  if (provider === "groq") {
    capabilities.push("Ultra-Fast Inference");
  }
  // Gemini 2.5 models support thinking
  if (modelId.includes("gemini-2.5")) {
    supportsThinking = true;
    thinkingBudgets = [1024, 2048, 4096];
  }

  const fallback: ModelInfo = {
    id: modelId,
    displayName,
    provider,
    maxInputTokens,
    maxOutputTokens,
    contextWindow,
    capabilities,
    description: `${displayName} from ${providerConfig?.name || provider}`,
    icon: providerConfig?.icon || "🤖",
    supportsTools,
    isMultimodal,
    supportsThinking,
    thinkingBudgets,
    groupName: providerConfig?.name || provider,
    modelProviders: [
      {
        provider,
        modelId,
        priority: 1,
        pricing: undefined,
      },
    ],
  };
  return fallback;
};

// After defining MODEL_INFO, normalize each entry to include groupName and ensure
// the base provider entry exists without overwriting any additional providers.
Object.values(MODEL_INFO).forEach((info) => {
  const group = PROVIDER_CONFIGS[info.provider]?.name || info.provider;

  // Ensure groupName is set
  if (!info.groupName) {
    info.groupName = group;
  }

  // Ensure modelProviders array exists
  if (!info.modelProviders || info.modelProviders.length === 0) {
    info.modelProviders = [
      {
        provider: info.provider,
        modelId: info.id,
        pricing: info.pricing,
        priority: 1,
      },
    ];
  } else {
    // Make sure the base provider is included exactly once
    if (!info.modelProviders.some((p) => p.provider === info.provider)) {
      info.modelProviders.push({
        provider: info.provider,
        modelId: info.id,
        pricing: info.pricing,
        priority: 1,
      });
    }

    // Sort providers by priority (ascending)
    info.modelProviders.sort((a, b) => a.priority - b.priority);
  }
});

// Dynamically add missing models from PROVIDER_CONFIGS to MODEL_INFO
Object.keys(PROVIDER_CONFIGS).forEach((providerKey) => {
  PROVIDER_CONFIGS[providerKey].models.forEach((modelId) => {
    if (!MODEL_INFO[modelId]) {
      const fallback = generateFallbackModelInfo(modelId);
      MODEL_INFO[modelId] = fallback;
      // normalize the newly added entry
      fallback.groupName = PROVIDER_CONFIGS[providerKey].name;
      fallback.modelProviders = [
        {
          provider: providerKey,
          modelId,
          pricing: fallback.pricing,
          priority: 1,
        },
      ];
    } else {
      // add cross-provider entry if missing
      const existing = MODEL_INFO[modelId];
      if (!existing.modelProviders) existing.modelProviders = [];
      if (!existing.modelProviders.some((p) => p.provider === providerKey)) {
        existing.modelProviders.push({
          provider: providerKey,
          modelId,
          pricing: existing.pricing,
          priority:
            existing.provider === providerKey
              ? 1
              : existing.modelProviders.length + 1,
        });
      }
      // Keep list ordered by priority
      existing.modelProviders.sort((a, b) => a.priority - b.priority);
    }
  });
});

/**
 * Resolve a provider-specific model ID to its canonical base model ID
 */
function resolveModelId(modelId: string): string | null {
  // Fast path: check if modelId exists as a key in MODEL_INFO
  if (MODEL_INFO[modelId]) {
    return modelId;
  }

  // Reverse lookup: iterate through all entries in MODEL_INFO
  for (const [baseModelId, modelInfo] of Object.entries(MODEL_INFO)) {
    // Check modelProviders array for any entry where entry.modelId === modelId
    if (modelInfo.modelProviders) {
      for (const providerEntry of modelInfo.modelProviders) {
        if (providerEntry.modelId === modelId) {
          return baseModelId;
        }
      }
    }
  }

  // Return null if no match is found
  return null;
}

/**
 * Get model information by ID (with fallback for unknown models)
 */
export const getModelInfo = (modelId: string): ModelInfo => {
  // Resolve canonical ID using reverse mapping
  const canonicalId = resolveModelId(modelId);

  // Use canonical ID if found, otherwise use original modelId
  const lookupId = canonicalId || modelId;

  // Perform MODEL_INFO lookup using the canonical ID
  const info = MODEL_INFO[lookupId] || generateFallbackModelInfo(modelId);
  if (!info.groupName) {
    info.groupName = PROVIDER_CONFIGS[info.provider]?.name || info.provider;
  }
  if (!info.modelProviders) {
    info.modelProviders = [
      {
        provider: info.provider,
        modelId: info.id,
        pricing: info.pricing,
        priority: 1,
      },
    ];
  }
  return info;
};

/**
 * Return ordered list of ProviderEntry for a given model
 */
export function getModelProviders(modelId: string): ProviderEntry[] {
  const info = getModelInfo(modelId);
  return info.modelProviders || [];
}

/**
 * Helper to estimate the relative cost of a provider for a given model.
 * We simply sum the input + output cost (per 1K tokens) as a rough proxy.
 * If explicit pricing is available on the ProviderEntry we use that, otherwise
 * we fall back to model-level pricing.
 */
function _getProviderCost(modelId: string, entry: ProviderEntry): number {
  if (entry.pricing) {
    return entry.pricing.input / 1000 + entry.pricing.output / 1000;
  }
  const pricing = getModelPricing(modelId, entry.provider);
  return pricing.input + pricing.output;
}

/**
 * Select the best available provider favouring the *cheapest* option while still
 * allowing the caller to constrain which providers are considered.
 *
 * If `availableProviders` is an empty array we treat it as "no constraint" and
 * consider **all** providers for the model.
 */
export function getBestAvailableProvider(
  modelId: string,
  availableProviders: string[]
): ProviderEntry | null {
  const allProviders = getModelProviders(modelId);
  const candidates =
    availableProviders.length === 0
      ? allProviders
      : allProviders.filter((p) => availableProviders.includes(p.provider));

  if (candidates.length === 0) return null;

  // Pick the provider with the lowest estimated cost. Break ties using the
  // existing priority field so user-defined overrides are still respected.
  return candidates.reduce((best, cur) => {
    const costBest = _getProviderCost(modelId, best);
    const costCur = _getProviderCost(modelId, cur);
    if (costCur === costBest) {
      return cur.priority < best.priority ? cur : best;
    }
    return costCur < costBest ? cur : best;
  });
}

/**
 * Return providers ordered by (1) estimated cost ascending, then (2) the
 * user-supplied priority value.  If a `preferredProvider` is supplied we still
 * move it to the front of the list to honour the caller's preference.
 */
export function getPrioritizedProviders(
  modelId: string,
  preferredProvider?: string
): ProviderEntry[] {
  const allProviders = getModelProviders(modelId);

  // Sort by cost then priority.
  const sortedProviders = [...allProviders].sort((a, b) => {
    const costDiff =
      _getProviderCost(modelId, a) - _getProviderCost(modelId, b);
    if (costDiff === 0) {
      return a.priority - b.priority;
    }
    return costDiff;
  });

  if (!preferredProvider) return sortedProviders;

  const preferredIndex = sortedProviders.findIndex(
    (p) => p.provider === preferredProvider
  );
  if (preferredIndex === -1) return sortedProviders;

  const [preferredEntry] = sortedProviders.splice(preferredIndex, 1);
  return [preferredEntry, ...sortedProviders];
}

/**
 * Get token pricing for a model with optional provider-specific pricing
 */
export function getModelPricing(
  modelId: string,
  provider?: string
): { input: number; output: number } {
  const modelInfo = getModelInfo(modelId);

  // If provider is specified, look for provider-specific pricing
  if (provider && modelInfo.modelProviders) {
    const providerEntry = modelInfo.modelProviders.find(
      (p) => p.provider === provider
    );
    if (providerEntry?.pricing) {
      // Convert from per 1M tokens to per 1K tokens
      return {
        input: providerEntry.pricing.input / 1000,
        output: providerEntry.pricing.output / 1000,
      };
    }
  }

  // Fall back to model-level pricing
  if (modelInfo.pricing) {
    // Convert from per 1M tokens to per 1K tokens
    return {
      input: modelInfo.pricing.input / 1000,
      output: modelInfo.pricing.output / 1000,
    };
  }

  // Default fallback pricing for unknown models (per 1K tokens)
  return { input: 0.001, output: 0.003 };
}

/**
 * Group all non-hidden models by their company/groupName
 */
export function getModelsByGroup(): Record<string, ModelInfo[]> {
  // Some provider aggregators (e.g. OpenRouter) expose third-party models under
  // their own name which results in duplicate, confusing groups appearing in
  // the UI.  We intentionally filter out these "umbrella" provider groups so
  // that the selector only displays meaningful `groupName`s (e.g. "DeepSeek",
  // "Meta", "OpenAI", …) defined on the underlying model.

  // Extend this set if additional provider-level groups need to be suppressed.
  const HIDDEN_PROVIDER_GROUPS = new Set<string>([
    "OpenRouterProvider",
    "GitHubProvider",
    "GroqProvider",
    "DeepSeekProvider",
    "GrokProvider",
    "CohereProvider",
    "MistralProvider",
    "CerebrasProvider",
    "GoogleProvider",
    "OpenAIProvider",
    "AnthropicProvider",
  ]);

  const result: Record<string, ModelInfo[]> = {};

  Object.values(MODEL_INFO).forEach((info) => {
    if (info.hidden) return;

    // Use the explicit groupName if present, otherwise fall back to provider
    // metadata.  We only want to surface *explicit* group names — if the model
    // had no groupName originally we treat it as a provider-level grouping and
    // therefore hide it.

    const explicitGroupName = (info as Partial<ModelInfo>).groupName;
    const groupName =
      explicitGroupName ||
      PROVIDER_CONFIGS[info.provider]?.name ||
      info.provider;

    // Hide umbrella provider groups and any models that lacked an explicit
    // groupName (explicitGroupName === undefined).
    if (!explicitGroupName || HIDDEN_PROVIDER_GROUPS.has(groupName)) return;

    if (!result[groupName]) {
      result[groupName] = [];
    }

    result[groupName].push(info);
  });

  return result;
}

// Helper function to determine if a model needs reasoning middleware
export function needsReasoningMiddleware(
  provider: string,
  model: string
): boolean {
  // Check if the model actually supports thinking/reasoning
  const modelInfo = getModelInfo(model);

  // Only apply reasoning middleware to models that support thinking
  if (!modelInfo.supportsThinking) {
    return false;
  }

  // Skip for known non-text generation models or legacy models that have issues
  const skipMiddleware = [
    // Skip for embedding models or other non-chat models
    "text-embedding",
    "embedding",
    "whisper", // Audio models
    "dall-e", // Image generation
    "tts", // Text-to-speech
  ];

  const shouldSkip = skipMiddleware.some((skip) =>
    model.toLowerCase().includes(skip.toLowerCase())
  );

  // Apply reasoning middleware only to thinking-capable chat/text generation models
  return !shouldSkip;
}

// Helper function to create enhanced model with reasoning middleware if needed
export function createEnhancedModel(
  aiModel: any,
  provider: string,
  model: string,
  wrapLanguageModel: any,
  extractReasoningMiddleware: any
) {
  if (needsReasoningMiddleware(provider, model)) {
    try {
      // Define reasoning tags based on provider and model type
      let tagNames = ["think", "reasoning", "thinking"];

      // Provider-specific reasoning patterns
      if (provider === "openai") {
        // OpenAI models like o1 use specific thinking patterns
        tagNames = ["thinking", "think", "reasoning", "analysis"];
      } else if (provider === "anthropic") {
        // Claude models often use thinking patterns
        tagNames = ["thinking", "analysis", "reasoning", "think"];
      } else if (provider === "google") {
        // Gemini models with thinking capabilities
        tagNames = ["thinking", "reasoning", "analysis", "think"];
      } else if (provider === "groq") {
        // Groq reasoning models like QwQ, DeepSeek R1
        tagNames = ["think", "reasoning", "thinking", "analysis"];
      } else if (provider === "deepseek") {
        // DeepSeek R1 and reasoning models
        tagNames = ["think", "reasoning", "thinking"];
      } else if (provider === "openrouter") {
        // OpenRouter hosts various reasoning models
        tagNames = ["think", "reasoning", "thinking", "analysis"];
      }

      // Try each tag name until one works
      for (const tagName of tagNames) {
        try {
          const enhancedModel = wrapLanguageModel({
            model: aiModel,
            middleware: extractReasoningMiddleware({
              tagName,
              // Use startWithReasoning for specific providers/models that benefit from it
              startWithReasoning:
                provider === "openrouter" ||
                provider === "groq" ||
                model.includes("qwq") ||
                model.includes("r1") ||
                model.includes("reasoning") ||
                model.includes("o1") ||
                model.includes("o3") ||
                model.includes("o4"),
            }),
          });

          return enhancedModel;
        } catch (tagError) {
          // Continue to next tag if this one fails
          console.warn(
            `Failed to use reasoning tag "${tagName}" for ${provider}/${model}:`,
            tagError
          );
          continue;
        }
      }

      // If all tag names fail, log warning and return base model
      console.warn(
        `All reasoning middleware tag names failed for ${provider}/${model}, using base model`
      );
      return aiModel;
    } catch (error) {
      console.warn(
        `Reasoning middleware not available for ${provider}/${model}, using base model:`,
        error
      );
      return aiModel;
    }
  }
  return aiModel;
}

// Image Generation Models
export interface ImageModelInfo {
  id: string;
  displayName: string;
  description: string;
  pricing: number; // Cost per image in dollars
  speed: "fast" | "medium" | "slow";
  quality: "standard" | "high" | "premium";
  provider: "cloudflare";
  cloudflareModel?: string; // The actual Cloudflare model ID
}

export const IMAGE_MODELS: Record<string, ImageModelInfo> = {
  "flux-1-schnell": {
    id: "flux-1-schnell",
    displayName: "Flux 1 Schnell",
    description: "Lightning fast, high-quality image generation",
    pricing: 0.03,
    speed: "fast",
    quality: "high",
    provider: "cloudflare",
    cloudflareModel: "@cf/black-forest-labs/flux-1-schnell",
  },
  "flux-1-dev": {
    id: "flux-1-dev",
    displayName: "Flux 1 Dev",
    description: "Advanced image generation with better quality",
    pricing: 0.05,
    speed: "medium",
    quality: "premium",
    provider: "cloudflare",
    cloudflareModel: "@cf/black-forest-labs/flux-1-dev",
  },
  "stable-diffusion-xl": {
    id: "stable-diffusion-xl",
    displayName: "Stable Diffusion XL",
    description: "High-resolution, detailed image generation",
    pricing: 0.04,
    speed: "medium",
    quality: "high",
    provider: "cloudflare",
    cloudflareModel: "@cf/stabilityai/stable-diffusion-xl-base-1.0",
  },
  "dreamshaper-8": {
    id: "dreamshaper-8",
    displayName: "DreamShaper 8",
    description: "Artistic and creative image generation",
    pricing: 0.03,
    speed: "fast",
    quality: "standard",
    provider: "cloudflare",
    cloudflareModel: "@cf/lykon/dreamshaper-8-lcm",
  },
};

export type ImageModelId = keyof typeof IMAGE_MODELS;

export const getImageModelInfo = (
  modelId: string
): ImageModelInfo | undefined => {
  return IMAGE_MODELS[modelId];
};

export const getImageModelDisplayName = (modelId: string): string => {
  return IMAGE_MODELS[modelId]?.displayName || modelId;
};
