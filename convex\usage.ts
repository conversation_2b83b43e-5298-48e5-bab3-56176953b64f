import { v } from "convex/values";
import { query, mutation } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";
import { MODEL_INFO, getModelPricing } from "../src/lib/models";

// Credit limits and maximum spending per plan
const PLAN_LIMITS = {
  free: {
    credits: 100,
    searches: 10,
    maxSpendingDollars: 1.0,
  },
  pro: {
    credits: 500,
    searches: 100,
    maxSpendingDollars: 8.0,
  },
  ultra: {
    credits: 2500,
    searches: 1000,
    maxSpendingDollars: 20.0,
  },
  max: {
    credits: 20000,
    searches: 5000,
    maxSpendingDollars: 120.0,
  },
};

// Convert dollars to credits (1 credit = $0.01)
function dollarsToCredits(dollars: number): number {
  return Math.ceil(dollars * 100); // 1 credit = $0.01
}

// Import image models from centralized location
import { IMAGE_MODELS } from "../src/lib/models";

// Calculate cost in credits for image generation
function calculateImageCost(imageModel: string): number {
  const model = IMAGE_MODELS[imageModel];
  const pricing = model?.pricing || IMAGE_MODELS["fast-image-ai"].pricing;
  return dollarsToCredits(pricing);
}

// Calculate cost in credits based on token usage
function calculateTokenCost(
  model: string,
  inputTokens: number,
  outputTokens: number,
  provider?: string
): number {
  const pricing = getModelPricing(model, provider);

  const inputCost = (inputTokens / 1000) * pricing.input;
  const outputCost = (outputTokens / 1000) * pricing.output;
  const totalDollars = inputCost + outputCost;

  return Math.ceil(totalDollars * 100); // Ceil to next credit
}

// New function to calculate precise dollar cost
function calculatePreciseDollarCost(
  model: string,
  inputTokens: number,
  outputTokens: number,
  provider?: string
): number {
  const pricing = getModelPricing(model, provider);
  const inputCost = (inputTokens / 1000) * pricing.input;
  const outputCost = (outputTokens / 1000) * pricing.output;
  return inputCost + outputCost;
}

async function ensureUsageRecord(ctx: any, userId: any) {
  let usage = await ctx.db
    .query("userUsage")
    .withIndex("by_user", (q: any) => q.eq("userId", userId))
    .unique();

  if (!usage) {
    // Set reset date to exactly 1 month from now (30 days)
    const resetDate = new Date();
    resetDate.setTime(resetDate.getTime() + 30 * 24 * 60 * 60 * 1000);

    const planLimits = PLAN_LIMITS.free;
    const usageId = await ctx.db.insert("userUsage", {
      userId,
      plan: "free",
      creditsUsed: 0,
      creditsLimit: planLimits.credits,
      maxSpendingDollars: planLimits.maxSpendingDollars,
      dollarsSpent: 0,
      searchesUsed: 0,
      resetDate: resetDate.getTime(),
    });

    usage = await ctx.db.get(usageId);
  }

  // Check if subscription has expired and downgrade if necessary
  if (usage.stripeSubscriptionId && usage.subscriptionStatus) {
    const hasActiveSubscription =
      usage.subscriptionStatus === "active" ||
      usage.subscriptionStatus === "trialing";
    const isSubscriptionExpired =
      usage.currentPeriodEnd && Date.now() > usage.currentPeriodEnd;

    if (!hasActiveSubscription || isSubscriptionExpired) {
      // Downgrade to free plan
      const resetDate = new Date();
      resetDate.setTime(resetDate.getTime() + 30 * 24 * 60 * 60 * 1000);

      const planLimits = PLAN_LIMITS.free;
      await ctx.db.patch(usage._id, {
        plan: "free",
        creditsUsed: 0,
        creditsLimit: planLimits.credits,
        maxSpendingDollars: planLimits.maxSpendingDollars,
        dollarsSpent: 0,
        searchesUsed: 0,
        resetDate: resetDate.getTime(),
        // Clear subscription data if expired
        ...(isSubscriptionExpired && {
          subscriptionStatus: "canceled",
          stripeSubscriptionId: undefined,
          currentPeriodStart: undefined,
          currentPeriodEnd: undefined,
          cancelAtPeriodEnd: undefined,
        }),
      });

      usage = await ctx.db.get(usage._id);
    }
  }

  // For subscription users, ensure resetDate is always synced with currentPeriodEnd
  if (
    usage.stripeSubscriptionId &&
    usage.currentPeriodEnd &&
    usage.subscriptionStatus === "active"
  ) {
    // Validate currentPeriodEnd is not NaN or invalid
    if (usage.currentPeriodEnd > 0 && !isNaN(usage.currentPeriodEnd)) {
      // Update resetDate to match currentPeriodEnd for subscription users
      if (usage.resetDate !== usage.currentPeriodEnd) {
        console.log(
          "Syncing resetDate with currentPeriodEnd for subscription user:",
          {
            userId,
            oldResetDate: usage.resetDate,
            newResetDate: usage.currentPeriodEnd,
          }
        );
        await ctx.db.patch(usage._id, {
          resetDate: usage.currentPeriodEnd,
        });
        usage = await ctx.db.get(usage._id);
      }
    } else {
      console.error(
        "Invalid currentPeriodEnd detected:",
        usage.currentPeriodEnd
      );
      // Fix invalid currentPeriodEnd by setting a fallback
      const fallbackResetDate = Date.now() + 30 * 24 * 60 * 60 * 1000;
      await ctx.db.patch(usage._id, {
        resetDate: fallbackResetDate,
        currentPeriodEnd: fallbackResetDate,
      });
      usage = await ctx.db.get(usage._id);
    }
  }

  // Check if we need to reset monthly usage
  const shouldReset =
    usage.stripeSubscriptionId && usage.currentPeriodEnd
      ? Date.now() >= usage.currentPeriodEnd // For subscription users, reset at period end
      : Date.now() >= usage.resetDate; // For free users, reset after 30 days

  if (usage && shouldReset) {
    let nextResetDate: number;

    if (usage.stripeSubscriptionId && usage.currentPeriodEnd) {
      // For subscription users, the reset date is managed by Stripe billing cycle
      // Calculate next billing period (typically 30 days from current period end)
      nextResetDate = usage.currentPeriodEnd + 30 * 24 * 60 * 60 * 1000;
    } else {
      // For free users, set next reset to exactly 30 days from the current reset date
      const nextReset = new Date(usage.resetDate);
      nextReset.setTime(nextReset.getTime() + 30 * 24 * 60 * 60 * 1000);
      nextResetDate = nextReset.getTime();
    }

    // Validate nextResetDate is not NaN
    if (isNaN(nextResetDate) || nextResetDate <= 0) {
      console.error(
        "Invalid nextResetDate calculated, using fallback:",
        nextResetDate
      );
      nextResetDate = Date.now() + 30 * 24 * 60 * 60 * 1000;
    }

    const planLimits = PLAN_LIMITS[usage.plan as keyof typeof PLAN_LIMITS];
    await ctx.db.patch(usage._id, {
      creditsUsed: 0,
      creditsLimit: planLimits.credits,
      maxSpendingDollars: planLimits.maxSpendingDollars,
      dollarsSpent: 0,
      searchesUsed: 0,
      resetDate: nextResetDate,
    });

    usage = await ctx.db.get(usage._id);
  }

  return usage;
}

export const get = query({
  args: {},
  returns: v.object({
    _id: v.optional(v.id("userUsage")),
    _creationTime: v.number(),
    userId: v.id("users"),
    plan: v.union(
      v.literal("free"),
      v.literal("pro"),
      v.literal("ultra"),
      v.literal("max")
    ),
    creditsUsed: v.number(),
    creditsLimit: v.number(),
    maxSpendingDollars: v.number(),
    dollarsSpent: v.number(),
    searchesUsed: v.number(),
    resetDate: v.number(),
    // Stripe subscription fields
    stripeCustomerId: v.optional(v.string()),
    stripeSubscriptionId: v.optional(v.string()),
    subscriptionStatus: v.optional(v.string()),
    currentPeriodStart: v.optional(v.number()),
    currentPeriodEnd: v.optional(v.number()),
    cancelAtPeriodEnd: v.optional(v.boolean()),
  }),
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const usage = await ctx.db
      .query("userUsage")
      .withIndex("by_user", (q: any) => q.eq("userId", userId))
      .unique();

    // Return existing usage or default values
    if (usage) {
      return usage;
    }

    // Return default usage structure if none exists
    const planLimits = PLAN_LIMITS.free;
    return {
      _id: undefined,
      _creationTime: Date.now(),
      userId,
      plan: "free" as const,
      creditsUsed: 0,
      creditsLimit: planLimits.credits,
      maxSpendingDollars: planLimits.maxSpendingDollars,
      dollarsSpent: 0,
      searchesUsed: 0,
      resetDate: Date.now() + 30 * 24 * 60 * 60 * 1000, // 30 days from now
      stripeCustomerId: undefined,
      stripeSubscriptionId: undefined,
      subscriptionStatus: undefined,
      currentPeriodStart: undefined,
      currentPeriodEnd: undefined,
      cancelAtPeriodEnd: undefined,
    };
  },
});

export const getLimits = query({
  args: {},
  returns: v.object({
    credits: v.number(),
    searches: v.number(),
    maxSpendingDollars: v.number(),
  }),
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return PLAN_LIMITS.free;
    }

    const usage = await ctx.db
      .query("userUsage")
      .withIndex("by_user", (q: any) => q.eq("userId", userId))
      .unique();

    const plan = usage?.plan || "free";
    return PLAN_LIMITS[plan];
  },
});

export const checkCreditsAvailable = query({
  args: {
    model: v.string(),
    estimatedInputTokens: v.number(),
    estimatedOutputTokens: v.number(),
    provider: v.optional(v.string()),
  },
  returns: v.object({
    hasCredits: v.boolean(),
    requiredCredits: v.number(),
    availableCredits: v.number(),
    wouldExceedSpending: v.boolean(),
  }),
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Get existing usage record without creating one (queries are read-only)
    const existingUsage = await ctx.db
      .query("userUsage")
      .withIndex("by_user", (q: any) => q.eq("userId", userId))
      .unique();

    // Create a usage object with proper defaults
    let usage: {
      plan: "free" | "pro" | "ultra" | "max";
      creditsUsed: number;
      creditsLimit: number;
      maxSpendingDollars: number;
      dollarsSpent: number;
      resetDate: number;
    };

    if (!existingUsage) {
      // Use default free plan values if no usage record exists
      const planLimits = PLAN_LIMITS.free;
      usage = {
        plan: "free",
        creditsUsed: 0,
        creditsLimit: planLimits.credits,
        maxSpendingDollars: planLimits.maxSpendingDollars,
        dollarsSpent: 0,
        resetDate: Date.now() + 30 * 24 * 60 * 60 * 1000, // 30 days from now
      };
    } else {
      // Check if we need to reset monthly usage (but don't actually reset in a query)
      if (Date.now() >= existingUsage.resetDate) {
        const planLimits = PLAN_LIMITS[existingUsage.plan];
        usage = {
          plan: existingUsage.plan,
          creditsUsed: 0,
          creditsLimit: planLimits.credits,
          maxSpendingDollars: planLimits.maxSpendingDollars,
          dollarsSpent: 0,
          resetDate: existingUsage.resetDate,
        };
      } else {
        usage = {
          plan: existingUsage.plan,
          creditsUsed: existingUsage.creditsUsed,
          creditsLimit: existingUsage.creditsLimit,
          maxSpendingDollars: existingUsage.maxSpendingDollars,
          dollarsSpent: existingUsage.dollarsSpent,
          resetDate: existingUsage.resetDate,
        };
      }
    }

    const requiredDollars = calculatePreciseDollarCost(
      args.model,
      args.estimatedInputTokens,
      args.estimatedOutputTokens,
      args.provider
    );
    const requiredCredits = Math.ceil(requiredDollars * 100);

    const availableCredits = usage.creditsLimit - usage.creditsUsed;
    const hasCredits = availableCredits >= requiredCredits;

    // Check if this would exceed spending limit
    const wouldExceedSpending =
      usage.dollarsSpent + requiredDollars > usage.maxSpendingDollars;

    return {
      hasCredits,
      requiredCredits,
      availableCredits,
      wouldExceedSpending,
    };
  },
});

export const deductCredits = mutation({
  args: {
    model: v.string(),
    inputTokens: v.number(),
    outputTokens: v.number(),
    extraDollars: v.optional(v.number()),
    provider: v.optional(v.string()),
  },
  returns: v.object({
    creditsDeducted: v.number(),
    dollarsSpent: v.number(),
    remainingCredits: v.number(),
  }),
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const usage = await ensureUsageRecord(ctx, userId);
    if (!usage) {
      throw new Error("Failed to create usage record");
    }

    const creditsToDeductTokens = calculateTokenCost(
      args.model,
      args.inputTokens,
      args.outputTokens,
      args.provider
    );

    const extraDollars = args.extraDollars ?? 0;
    const extraCredits = Math.ceil(extraDollars * 100);
    const totalCreditsToDeduct = creditsToDeductTokens + extraCredits;

    const tokenDollarCost = calculatePreciseDollarCost(
      args.model,
      args.inputTokens,
      args.outputTokens,
      args.provider
    );

    const preciseDollars = tokenDollarCost + extraDollars;

    // Check if user has enough credits
    const availableCredits = usage.creditsLimit - usage.creditsUsed;
    if (availableCredits < totalCreditsToDeduct) {
      throw new Error(
        `Insufficient credits. Required: ${totalCreditsToDeduct}, Available: ${availableCredits}`
      );
    }

    // Check spending limit with precise dollars
    if (usage.dollarsSpent + preciseDollars > usage.maxSpendingDollars) {
      throw new Error(
        `Would exceed monthly spending limit of $${usage.maxSpendingDollars}`
      );
    }

    const newCreditsUsed = usage.creditsUsed + totalCreditsToDeduct;
    const newDollarsSpent = usage.dollarsSpent + preciseDollars;

    await ctx.db.patch(usage._id, {
      creditsUsed: newCreditsUsed,
      dollarsSpent: newDollarsSpent,
    });

    // Log usage event for analytics
    await ctx.db.insert("usageEvents", {
      userId,
      type: "chat",
      model: args.model,
      provider: args.provider,
      credits: totalCreditsToDeduct,
      dollars: preciseDollars,
      inputTokens: args.inputTokens,
      outputTokens: args.outputTokens,
      createdAt: Date.now(),
    });

    return {
      creditsDeducted: totalCreditsToDeduct,
      dollarsSpent: preciseDollars,
      remainingCredits: usage.creditsLimit - newCreditsUsed,
    };
  },
});

export const incrementSearches = mutation({
  args: {},
  returns: v.null(),
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const usage = await ensureUsageRecord(ctx, userId);
    if (!usage) {
      throw new Error("Failed to create usage record");
    }

    const limits = PLAN_LIMITS[usage.plan as keyof typeof PLAN_LIMITS];
    if (usage.searchesUsed >= limits.searches) {
      throw new Error(
        `Monthly search limit reached (${limits.searches}). Upgrade your plan for more searches.`
      );
    }

    await ctx.db.patch(usage._id, {
      searchesUsed: usage.searchesUsed + 1,
    });

    // Log usage event for analytics (searches don't cost credits/dollars)
    await ctx.db.insert("usageEvents", {
      userId,
      type: "search",
      credits: 0,
      dollars: 0,
      createdAt: Date.now(),
    });

    return null;
  },
});

export const checkImageCreditsAvailable = query({
  args: {
    imageModel: v.string(),
  },
  returns: v.object({
    hasCredits: v.boolean(),
    requiredCredits: v.number(),
    availableCredits: v.number(),
    wouldExceedSpending: v.boolean(),
  }),
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Get existing usage record without creating one (queries are read-only)
    const existingUsage = await ctx.db
      .query("userUsage")
      .withIndex("by_user", (q: any) => q.eq("userId", userId))
      .unique();

    // Create a usage object with proper defaults
    let usage: {
      plan: "free" | "pro" | "ultra" | "max";
      creditsUsed: number;
      creditsLimit: number;
      maxSpendingDollars: number;
      dollarsSpent: number;
      resetDate: number;
    };

    if (!existingUsage) {
      // Use default free plan values if no usage record exists
      const planLimits = PLAN_LIMITS.free;
      usage = {
        plan: "free",
        creditsUsed: 0,
        creditsLimit: planLimits.credits,
        maxSpendingDollars: planLimits.maxSpendingDollars,
        dollarsSpent: 0,
        resetDate: Date.now() + 30 * 24 * 60 * 60 * 1000, // 30 days from now
      };
    } else {
      // Check if we need to reset monthly usage (but don't actually reset in a query)
      if (Date.now() >= existingUsage.resetDate) {
        const planLimits = PLAN_LIMITS[existingUsage.plan];
        usage = {
          plan: existingUsage.plan,
          creditsUsed: 0,
          creditsLimit: planLimits.credits,
          maxSpendingDollars: planLimits.maxSpendingDollars,
          dollarsSpent: 0,
          resetDate: existingUsage.resetDate,
        };
      } else {
        usage = {
          plan: existingUsage.plan,
          creditsUsed: existingUsage.creditsUsed,
          creditsLimit: existingUsage.creditsLimit,
          maxSpendingDollars: existingUsage.maxSpendingDollars,
          dollarsSpent: existingUsage.dollarsSpent,
          resetDate: existingUsage.resetDate,
        };
      }
    }

    const requiredCredits = calculateImageCost(args.imageModel);
    const availableCredits = usage.creditsLimit - usage.creditsUsed;
    const hasCredits = availableCredits >= requiredCredits;

    // Check if this would exceed spending limit
    const requiredDollars = requiredCredits / 100; // Convert credits back to dollars
    const wouldExceedSpending =
      usage.dollarsSpent + requiredDollars > usage.maxSpendingDollars;

    return {
      hasCredits,
      requiredCredits,
      availableCredits,
      wouldExceedSpending,
    };
  },
});

export const deductImageCredits = mutation({
  args: {
    imageModel: v.string(),
  },
  returns: v.object({
    creditsDeducted: v.number(),
    dollarsSpent: v.number(),
    remainingCredits: v.number(),
  }),
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const usage = await ensureUsageRecord(ctx, userId);
    if (!usage) {
      throw new Error("Failed to create usage record");
    }

    const creditsToDeduct = calculateImageCost(args.imageModel);
    const dollarsToSpend = creditsToDeduct / 100; // Convert credits to dollars

    // Check if user has enough credits
    const availableCredits = usage.creditsLimit - usage.creditsUsed;
    if (availableCredits < creditsToDeduct) {
      throw new Error(
        `Insufficient credits. Required: ${creditsToDeduct}, Available: ${availableCredits}`
      );
    }

    // Check spending limit
    if (usage.dollarsSpent + dollarsToSpend > usage.maxSpendingDollars) {
      throw new Error(
        `Would exceed monthly spending limit of $${usage.maxSpendingDollars}`
      );
    }

    const newCreditsUsed = usage.creditsUsed + creditsToDeduct;
    const newDollarsSpent = usage.dollarsSpent + dollarsToSpend;

    await ctx.db.patch(usage._id, {
      creditsUsed: newCreditsUsed,
      dollarsSpent: newDollarsSpent,
    });

    // Log usage event for analytics
    await ctx.db.insert("usageEvents", {
      userId,
      type: "image",
      model: args.imageModel,
      credits: creditsToDeduct,
      dollars: dollarsToSpend,
      createdAt: Date.now(),
    });

    return {
      creditsDeducted: creditsToDeduct,
      dollarsSpent: dollarsToSpend,
      remainingCredits: usage.creditsLimit - newCreditsUsed,
    };
  },
});

export const upgradePlan = mutation({
  args: {
    plan: v.union(
      v.literal("free"),
      v.literal("pro"),
      v.literal("ultra"),
      v.literal("max")
    ),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const usage = await ensureUsageRecord(ctx, userId);
    if (!usage) {
      throw new Error("Failed to create usage record");
    }

    const newPlanLimits = PLAN_LIMITS[args.plan];
    await ctx.db.patch(usage._id, {
      plan: args.plan,
      creditsLimit: newPlanLimits.credits,
      maxSpendingDollars: newPlanLimits.maxSpendingDollars,
    });

    return null;
  },
});

// Utility function to get token pricing info
export const getTokenPricing = query({
  args: {},
  returns: v.record(
    v.string(),
    v.object({
      input: v.number(),
      output: v.number(),
    })
  ),
  handler: async (_ctx) => {
    // Build pricing record from MODEL_INFO
    const pricing: Record<string, { input: number; output: number }> = {};

    for (const [modelId, modelInfo] of Object.entries(MODEL_INFO)) {
      if (modelInfo.pricing) {
        pricing[modelId] = {
          input: modelInfo.pricing.input / 1000,
          output: modelInfo.pricing.output / 1000,
        };
      }
    }

    return pricing;
  },
});

export const getUsageBreakdown = query({
  args: {
    days: v.optional(v.number()),
  },
  returns: v.object({
    modelBreakdown: v.array(
      v.object({
        model: v.optional(v.string()),
        provider: v.optional(v.string()),
        type: v.string(),
        totalCredits: v.number(),
        totalDollars: v.number(),
        eventCount: v.number(),
        inputTokens: v.optional(v.number()),
        outputTokens: v.optional(v.number()),
      })
    ),
    dailyUsage: v.array(
      v.object({
        date: v.string(),
        totalCredits: v.number(),
        totalDollars: v.number(),
        eventCount: v.number(),
        chatEvents: v.number(),
        imageEvents: v.number(),
        searchEvents: v.number(),
      })
    ),
    typeBreakdown: v.array(
      v.object({
        type: v.string(),
        totalCredits: v.number(),
        totalDollars: v.number(),
        eventCount: v.number(),
      })
    ),
    providerBreakdown: v.array(
      v.object({
        provider: v.optional(v.string()),
        totalCredits: v.number(),
        totalDollars: v.number(),
        eventCount: v.number(),
      })
    ),
  }),
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const days = args.days || 30;
    const cutoffTime = Date.now() - days * 24 * 60 * 60 * 1000;

    // Get all usage events for the user within the time period
    const usageEvents = await ctx.db
      .query("usageEvents")
      .withIndex("by_user_date", (q: any) =>
        q.eq("userId", userId).gte("createdAt", cutoffTime)
      )
      .collect();

    // Model breakdown
    const modelMap = new Map<
      string,
      {
        model?: string;
        provider?: string;
        type: string;
        totalCredits: number;
        totalDollars: number;
        eventCount: number;
        inputTokens?: number;
        outputTokens?: number;
      }
    >();

    // Daily usage breakdown
    const dailyMap = new Map<
      string,
      {
        date: string;
        totalCredits: number;
        totalDollars: number;
        eventCount: number;
        chatEvents: number;
        imageEvents: number;
        searchEvents: number;
      }
    >();

    // Type breakdown
    const typeMap = new Map<
      string,
      {
        type: string;
        totalCredits: number;
        totalDollars: number;
        eventCount: number;
      }
    >();

    // Provider breakdown
    const providerMap = new Map<
      string,
      {
        provider?: string;
        totalCredits: number;
        totalDollars: number;
        eventCount: number;
      }
    >();

    for (const event of usageEvents) {
      // Model breakdown
      const modelKey = `${event.model || "unknown"}-${event.provider || "unknown"}-${event.type}`;
      const existing = modelMap.get(modelKey);
      if (existing) {
        existing.totalCredits += event.credits;
        existing.totalDollars += event.dollars;
        existing.eventCount += 1;
        if (event.inputTokens)
          existing.inputTokens =
            (existing.inputTokens || 0) + event.inputTokens;
        if (event.outputTokens)
          existing.outputTokens =
            (existing.outputTokens || 0) + event.outputTokens;
      } else {
        modelMap.set(modelKey, {
          model: event.model,
          provider: event.provider,
          type: event.type,
          totalCredits: event.credits,
          totalDollars: event.dollars,
          eventCount: 1,
          inputTokens: event.inputTokens,
          outputTokens: event.outputTokens,
        });
      }

      // Daily breakdown
      const date = new Date(event.createdAt).toISOString().split("T")[0];
      const dailyExisting = dailyMap.get(date);
      if (dailyExisting) {
        dailyExisting.totalCredits += event.credits;
        dailyExisting.totalDollars += event.dollars;
        dailyExisting.eventCount += 1;
        if (event.type === "chat") dailyExisting.chatEvents += 1;
        else if (event.type === "image") dailyExisting.imageEvents += 1;
        else if (event.type === "search") dailyExisting.searchEvents += 1;
      } else {
        dailyMap.set(date, {
          date,
          totalCredits: event.credits,
          totalDollars: event.dollars,
          eventCount: 1,
          chatEvents: event.type === "chat" ? 1 : 0,
          imageEvents: event.type === "image" ? 1 : 0,
          searchEvents: event.type === "search" ? 1 : 0,
        });
      }

      // Type breakdown
      const typeExisting = typeMap.get(event.type);
      if (typeExisting) {
        typeExisting.totalCredits += event.credits;
        typeExisting.totalDollars += event.dollars;
        typeExisting.eventCount += 1;
      } else {
        typeMap.set(event.type, {
          type: event.type,
          totalCredits: event.credits,
          totalDollars: event.dollars,
          eventCount: 1,
        });
      }

      // Provider breakdown
      const providerKey = event.provider || "unknown";
      const providerExisting = providerMap.get(providerKey);
      if (providerExisting) {
        providerExisting.totalCredits += event.credits;
        providerExisting.totalDollars += event.dollars;
        providerExisting.eventCount += 1;
      } else {
        providerMap.set(providerKey, {
          provider: event.provider,
          totalCredits: event.credits,
          totalDollars: event.dollars,
          eventCount: 1,
        });
      }
    }

    return {
      modelBreakdown: Array.from(modelMap.values()).sort(
        (a, b) => b.totalCredits - a.totalCredits
      ),
      dailyUsage: Array.from(dailyMap.values()).sort((a, b) =>
        a.date.localeCompare(b.date)
      ),
      typeBreakdown: Array.from(typeMap.values()).sort(
        (a, b) => b.totalCredits - a.totalCredits
      ),
      providerBreakdown: Array.from(providerMap.values()).sort(
        (a, b) => b.totalCredits - a.totalCredits
      ),
    };
  },
});
