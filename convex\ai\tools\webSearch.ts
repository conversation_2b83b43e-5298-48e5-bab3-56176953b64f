"use node";

import { tool } from "ai";
import { z } from "zod";
import { performExaSearch, performExaResearch } from "../utils/search";
import { api } from "../../_generated/api";
import { jsonSchema } from "ai";

export function createWebSearchTool(ctx: any, usingUserKey: boolean) {
  return tool({
    description: `Run a **quick web search** for up-to-date facts, headlines or links. Returns rich snippets and images when available. Returns up to 20 rich results with thumbnails when available.\n\nWhen to use:\n• You need recent news, live data, or external references.\n• You want a list of sources to decide which ones to read in full (via \`url_fetch\`).\n• This should be your FIRST choice for most search needs.\n\nAfter calling this, usually follow-up with \`url_fetch\` on 1-3 of the most relevant links if the snippets are insufficient.`,
    inputSchema: jsonSchema({
      type: "object",
      properties: {
        query: {
          type: "string",
          description: "The search query for current/real-time information",
        },
      },
      required: ["query"],
      additionalProperties: false,
    }),
    execute: async (args: any): Promise<string> => {
      const { query } = args;
      // Handle empty query
      if (!query || query.trim() === "") {
        return `Error: No search query provided.

Please provide a specific search query such as:
- "latest AI developments 2024"
- "current weather New York"  
- "best practices for React hooks"
- "recent news about climate change"`;
      }

      return await performExaSearch(
        ctx,
        query.trim(),
        "advanced",
        !usingUserKey
      );
    },
  });
}

export function createResearchTool(ctx: any, usingUserKey: boolean) {
  return tool({
    description: `Run Exa.ai **autonomous deep research**. This tool performs comprehensive, multi-source research on complex topics.

⚠️ **USE SPARINGLY** - Only use when:
• User explicitly asks for "research" or "comprehensive analysis"
• Topic requires extensive, up-to-date information from many sources
• Simple web search or deep search would be insufficient
• You need autonomous research across multiple domains

**Try web_search or deep_search FIRST** for most information needs.

USAGE: You MUST call this tool with an 'instructions' parameter containing your research question.

Example calls:
- research({ instructions: "comprehensive analysis of AI trends in 2024" })
- research({ instructions: "multi-faceted impact of climate change on global agriculture" })

Before calling, send a message telling the user that you are starting comprehensive research.`,
    inputSchema: jsonSchema({
      type: "object",
      properties: {
        instructions: {
          type: "string",
          description:
            "The research question or topic you want to investigate. This must be a meaningful question or topic.",
        },
      },
      required: ["instructions"],
      additionalProperties: false,
    }),
    execute: async (args: any): Promise<string> => {
      const { instructions } = args;
      try {
        // Handle null or empty instructions
        if (!instructions || instructions.trim() === "") {
          return "Error: Research tool requires an 'instructions' parameter with your research question or topic. Please provide a specific research question.";
        }

        const cleanInstructions = instructions.trim();
        if (cleanInstructions.length < 3) {
          return "Error: Research instructions must be at least 3 characters long. Please provide a meaningful research question.";
        }

        return await performExaResearch(ctx, cleanInstructions);
      } catch (error) {
        const errorMsg = error instanceof Error ? error.message : String(error);
        console.error(`Research tool error:`, error);
        return `Research execution error: ${errorMsg}`;
      }
    },
  });
}

export function createDeepSearchTool(ctx: any, usingUserKey: boolean) {
  return tool({
    description: `Run an **in-depth multi-query search**. Best for gathering multiple perspectives or when a single search isn't enough. It issues several focused searches and merges up to 20 results per query.\n\nWhen to use:\n• You must gather perspectives from many sources, or the single web search didn't yield enough detail.\n• You need multiple angles on a topic but don't require full autonomous research.\n• This is your SECOND choice after web_search but BEFORE research tool.\n\nTypical follow-up: use \`url_fetch\` on promising links to extract full content or facts.`,
    inputSchema: jsonSchema({
      type: "object",
      properties: {
        query: {
          type: "string",
          description: "The main research topic requiring current information",
        },
        related_queries: {
          type: "array",
          items: { type: "string" },
          description:
            "Additional specific research angles for comprehensive coverage. Leave empty for single query search.",
        },
      },
      required: ["query", "related_queries"],
      additionalProperties: false,
    }),
    execute: async (args: any) => {
      try {
        const { query, sources } = args;
        let { related_queries = [] } = args;

        // Ensure related_queries is an array
        if (!Array.isArray(related_queries)) {
          related_queries = [];
        }

        // Handle empty query
        if (!query || query.trim() === "") {
          return `Error: No search query provided for deep search.

Please provide a main research topic such as:
- "artificial intelligence trends"
- "renewable energy technologies"
- "remote work productivity"
- "cybersecurity best practices"

You can also include related queries for multiple perspectives.`;
        }

        // Only increment search usage if using built-in keys
        if (!usingUserKey) {
          // Deep search uses additional search quota
          await ctx.runMutation(api.usage.incrementSearches);
          await ctx.runMutation(api.usage.incrementSearches);
        }

        const trimmedQuery = query.trim();
        const allQueries = [
          trimmedQuery,
          ...(related_queries || []).slice(0, 3),
        ]; // Limit to 4 total queries
        const searchPromises = allQueries.map((q) =>
          performExaSearch(ctx, q.trim(), "advanced", !usingUserKey)
        );
        const results = await Promise.all(searchPromises);

        let combinedResults = `Deep search results for "${trimmedQuery}":\n\n`;
        results.forEach((result, index) => {
          combinedResults += `=== Results for "${allQueries[index]}" ===\n${result}\n\n`;
        });

        return combinedResults;
      } catch (error) {
        const errorMsg = error instanceof Error ? error.message : String(error);
        console.error(`Deep search tool error:`, error);
        return `Deep search execution error: ${errorMsg}

Please try:
- Simplifying your search query
- Using the regular web_search tool instead
- Checking your internet connection`;
      }
    },
  });
}
