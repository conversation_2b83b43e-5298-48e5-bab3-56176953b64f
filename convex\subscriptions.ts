import { v } from "convex/values";
import { query } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";

// Get current subscription details
export const getSubscription = query({
  args: {},
  returns: v.union(
    v.object({
      hasSubscription: v.literal(true),
      plan: v.union(v.literal("pro"), v.literal("ultra"), v.literal("max")),
      status: v.string(),
      currentPeriodStart: v.number(),
      currentPeriodEnd: v.number(),
      cancelAtPeriodEnd: v.boolean(),
      stripeCustomerId: v.string(),
      stripeSubscriptionId: v.string(),
    }),
    v.object({
      hasSubscription: v.literal(false),
    })
  ),
  handler: async (
    ctx
  ): Promise<
    | { hasSubscription: false }
    | {
        hasSubscription: true;
        plan: "pro" | "ultra" | "max";
        status: string;
        currentPeriodStart: number;
        currentPeriodEnd: number;
        cancelAtPeriodEnd: boolean;
        stripeCustomerId: string;
        stripeSubscriptionId: string;
      }
  > => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return { hasSubscription: false };
    }

    const usage = await ctx.db
      .query("userUsage")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .unique();

    if (!usage?.stripeSubscriptionId || !usage.subscriptionStatus) {
      return { hasSubscription: false };
    }

    // Check if subscription is active
    if (
      usage.subscriptionStatus !== "active" &&
      usage.subscriptionStatus !== "trialing"
    ) {
      return { hasSubscription: false };
    }

    // Ensure we have a valid paid plan
    if (usage.plan === "free") {
      return { hasSubscription: false };
    }

    return {
      hasSubscription: true,
      plan: usage.plan,
      status: usage.subscriptionStatus,
      currentPeriodStart: usage.currentPeriodStart || 0,
      currentPeriodEnd: usage.currentPeriodEnd || 0,
      cancelAtPeriodEnd: usage.cancelAtPeriodEnd || false,
      stripeCustomerId: usage.stripeCustomerId!,
      stripeSubscriptionId: usage.stripeSubscriptionId,
    };
  },
});
