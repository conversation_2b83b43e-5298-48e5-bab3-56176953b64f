import {
  <PERSON>,
  <PERSON><PERSON>,
  Con<PERSON><PERSON>,
  <PERSON>,
  <PERSON>tm<PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Section,
  Text,
} from "@react-email/components";
import React from "react";

interface SubscriptionEmailProps {
  userEmail: string;
  userName?: string;
  planName: "pro" | "ultra" | "max";
  planPrice: string;
  currentPeriodEnd: string;
  dashboardUrl?: string;
}

const getPlanBadge = (plan: string) => {
  switch (plan) {
    case "pro": return "Professional";
    case "ultra": return "Advanced";
    case "max": return "Enterprise";
    default: return "Premium";
  }
};

const getPlanFeatures = (plan: string) => {
  switch (plan) {
    case "pro":
      return [
        "500 monthly AI credits",
        "100 web searches per month",
        "Access to premium AI models",
        "Priority support",
        "Advanced conversation features"
      ];
    case "ultra":
      return [
        "2,500 monthly AI credits",
        "1,000 web searches per month",
        "Unlimited access to all AI models",
        "Priority support",
        "Advanced conversation features",
        "API access",
        "Custom integrations"
      ];
    case "max":
      return [
        "20,000 monthly AI credits",
        "5,000 web searches per month",
        "Unlimited access to all AI models",
        "Premium support",
        "Advanced conversation features",
        "API access",
        "Custom integrations",
        "White-label options"
      ];
    default:
      return ["Access to premium features"];
  }
};

export const SubscriptionEmail = ({
  userEmail = "<EMAIL>",
  userName = "there",
  planName = "pro",
  planPrice = "$19.99",
  currentPeriodEnd = "January 1, 2025",
  dashboardUrl = "https://ai.erzen.tk/settings",
}: SubscriptionEmailProps) => {
  const planBadge = getPlanBadge(planName);
  const features = getPlanFeatures(planName);

  return (
    <Html>
      <Head />
      <Body style={main}>
        <Container style={container}>
          <Section style={header}>
            <Text style={headerText}>
              Welcome to {planName.charAt(0).toUpperCase() + planName.slice(1)}
            </Text>
          </Section>
          
          <Section style={content}>
            <Text style={greeting}>Your AI workspace is ready</Text>
            
            <Text style={paragraph}>
              Welcome to Erzen AI {planName.charAt(0).toUpperCase() + planName.slice(1)}. 
              Your subscription is now active with full access to advanced AI capabilities, 
              autonomous execution, and premium model access.
            </Text>
            
            <Section style={subscriptionDetails}>
              <Text style={detailsTitle}>Subscription Details</Text>
              <Text style={detailItem}><strong>Plan:</strong> {planName.charAt(0).toUpperCase() + planName.slice(1)}</Text>
              <Text style={detailItem}><strong>Price:</strong> {planPrice}/month</Text>
              <Text style={detailItem}><strong>Next billing date:</strong> {currentPeriodEnd}</Text>
              <Text style={detailItem}><strong>Email:</strong> {userEmail}</Text>
            </Section>
            
            <Section style={buttonContainer}>
              <Button style={button} href={dashboardUrl}>
                Access Your Dashboard
              </Button>
            </Section>
            
            <Section style={featuresBox}>
              <Text style={featuresTitle}>Your {planName.charAt(0).toUpperCase() + planName.slice(1)} capabilities:</Text>
              <ul style={featuresList}>
                {features.map((feature, index) => (
                  <li key={index}>{feature}</li>
                ))}
              </ul>
            </Section>

            <Section style={tipsBox}>
              <Text style={tipsTitle}>Getting Started:</Text>
              <ul style={featuresList}>
                <li>Access premium AI models in your workspace</li>
                <li>Upload and analyze documents with advanced AI</li>
                <li>Enable autonomous agent execution</li>
                <li>Monitor usage and credits in your dashboard</li>
                <li>Set up integrations and automations</li>
              </ul>
            </Section>
            
            <Hr style={hr} />
            
            <Text style={support}>
              Need help getting started? We're here to help! 
              <Link href="mailto:<EMAIL>" style={link}>
                Contact our support team
              </Link> or visit our 
              <Link href={dashboardUrl} style={link}> dashboard</Link> to manage your subscription.
            </Text>
            
            <Text style={disclaimer}>
              You can manage your subscription, view invoices, and update your billing information 
              in your dashboard at any time.
            </Text>
          </Section>
          
          <Section style={footer}>
            <Text style={footerText}>
              © 2024 Erzen AI. Conversations at the speed of thought.
            </Text>
            <Text style={footerText}>
              <Link href={dashboardUrl} style={footerLink}>Manage Subscription</Link> • 
              <Link href="mailto:<EMAIL>" style={footerLink}> Support</Link>
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
};

// Styles - Modern Dark Theme
const main = {
  backgroundColor: "#000000",
  fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
  color: "#d4d4d8",
};

const container = {
  backgroundColor: "#09090b",
  margin: "0 auto",
  padding: "0",
  marginTop: "30px",
  marginBottom: "30px",
  borderRadius: "16px",
  border: "1px solid #27272a",
  maxWidth: "600px",
  overflow: "hidden",
};

const header = {
  background: "linear-gradient(135deg, #18181b 0%, #27272a 100%)",
  padding: "40px 32px",
  textAlign: "center" as const,
  borderBottom: "1px solid #27272a",
};

const headerText = {
  color: "#ffffff",
  fontSize: "32px",
  fontWeight: "bold",
  margin: "0",
  letterSpacing: "-0.025em",
};

const content = {
  padding: "32px",
  backgroundColor: "#09090b",
};

const greeting = {
  fontSize: "20px",
  fontWeight: "600",
  color: "#ffffff",
  marginTop: "0",
  marginBottom: "24px",
};

const paragraph = {
  fontSize: "16px",
  lineHeight: "1.7",
  color: "#a1a1aa",
  marginBottom: "24px",
};

const subscriptionDetails = {
  backgroundColor: "#18181b",
  padding: "24px",
  borderRadius: "12px",
  border: "1px solid #27272a",
  margin: "24px 0",
};

const detailsTitle = {
  fontSize: "18px",
  fontWeight: "600",
  color: "#ffffff",
  marginTop: "0",
  marginBottom: "16px",
};

const detailItem = {
  fontSize: "15px",
  color: "#a1a1aa",
  margin: "12px 0",
  lineHeight: "1.6",
};

const buttonContainer = {
  textAlign: "center" as const,
  margin: "32px 0",
};

const button = {
  background: "linear-gradient(135deg, #18181b 0%, #27272a 100%)",
  borderRadius: "12px",
  color: "#ffffff",
  fontSize: "16px",
  fontWeight: "600",
  textDecoration: "none",
  textAlign: "center" as const,
  display: "inline-block",
  border: "1px solid #3f3f46",
  padding: "16px 32px",
};

const featuresBox = {
  backgroundColor: "#18181b",
  padding: "24px",
  borderRadius: "12px",
  margin: "24px 0",
  border: "1px solid #27272a",
};

const featuresTitle = {
  marginTop: "0",
  fontSize: "16px",
  fontWeight: "600",
  color: "#ffffff",
  marginBottom: "16px",
};

const featuresList = {
  margin: "0",
  paddingLeft: "20px",
  color: "#a1a1aa",
  fontSize: "15px",
  lineHeight: "1.6",
};

const tipsBox = {
  backgroundColor: "#18181b",
  padding: "24px",
  borderRadius: "12px",
  margin: "24px 0",
  border: "1px solid #27272a",
};

const tipsTitle = {
  marginTop: "0",
  fontSize: "16px",
  fontWeight: "600",
  color: "#ffffff",
  marginBottom: "16px",
};

const hr = {
  borderColor: "#27272a",
  margin: "32px 0",
  borderWidth: "1px",
  borderStyle: "solid",
};

const support = {
  fontSize: "14px",
  color: "#a1a1aa",
  lineHeight: "1.6",
  marginBottom: "16px",
};

const disclaimer = {
  fontSize: "14px",
  color: "#71717a",
  lineHeight: "1.6",
  marginBottom: "16px",
};

const link = {
  color: "#a1a1aa",
  textDecoration: "underline",
};

const footer = {
  textAlign: "center" as const,
  marginTop: "24px",
  padding: "24px",
  backgroundColor: "#18181b",
  borderTop: "1px solid #27272a",
};

const footerText = {
  fontSize: "12px",
  color: "#71717a",
  margin: "8px 0",
};

const footerLink = {
  color: "#71717a",
  textDecoration: "underline",
};

export default SubscriptionEmail;