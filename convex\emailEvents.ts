import { internalMutation } from "./_generated/server";
import { v } from "convex/values";

// Email event handler for tracking delivery status
export const handleEmailEvent = internalMutation({
  args: {
    id: v.string(), // This would be vEmailId from the Resend component
    event: v.object({
      type: v.string(),
      createdAt: v.string(),
      data: v.optional(v.any()),
    }),
  },
  returns: v.null(),
  handler: async (ctx, args) => {
    console.log("Email event received:", args.id, args.event);
    
    // Here you could store email delivery status in your database
    // For example, tracking which emails were delivered, bounced, etc.
    
    // You might want to create an emailEvents table to track this:
    // await ctx.db.insert("emailEvents", {
    //   emailId: args.id,
    //   eventType: args.event.type,
    //   eventData: args.event.data,
    //   createdAt: Date.now(),
    // });

    return null;
  },
});