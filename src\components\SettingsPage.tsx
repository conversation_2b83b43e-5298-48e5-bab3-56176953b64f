import { useState, useEffect, Suspense } from "react";
import { SettingsPageHeader } from "./settings/SettingsPageHeader";
import {
  SettingsSidebar,
} from "./settings/SettingsSidebar";
import { AccountSection } from "./settings/AccountSection";
import { AppearanceSection } from "./settings/AppearanceSection";
import { AISection } from "./settings/AISection";
import { ApiKeysSection } from "./settings/ApiKeysSection";
import { DataSection } from "./settings/DataSection";
import { McpSection } from "./settings/McpSection";
import { N8nSection } from "./settings/N8nSection";
import { UsageSection } from "./settings/UsageSection";
import { CustomProvidersSection } from "./settings/CustomProvidersSection";
import { SettingsSkeleton } from "./settings/SettingsSkeleton";

/**
 * SettingsPage Component - Dark Mode Only
 * 
 * This component is designed exclusively for dark mode and uses
 * sophisticated dark-themed styling that is not compatible with light mode.
 * The component automatically forces dark mode via the "dark" class.
 */

interface SettingsPageProps {
  onBack: () => void;
}

const sectionComponents: Record<string, React.ComponentType> = {
  account: AccountSection,
  appearance: AppearanceSection,
  ai: AISection,
  apiKeys: ApiKeysSection,
  customProviders: CustomProvidersSection,
  data: DataSection,
  mcp: McpSection,
  n8n: N8nSection,
  usage: UsageSection,
};

// Map sections to their appropriate skeleton variants
const sectionSkeletonVariants: Record<string, "simple" | "complex" | "list" | "usage" | "grid"> = {
  account: "simple",
  appearance: "grid",
  ai: "complex",
  apiKeys: "grid",
  customProviders: "list",
  data: "complex",
  mcp: "list",
  n8n: "list",
  usage: "usage",
};

export function SettingsPage({ onBack }: SettingsPageProps) {
  const [activeSection, setActiveSection] = useState<keyof typeof sectionComponents>(
    "ai",
  );
  const [isTransitioning, setIsTransitioning] = useState(false);

  const ActiveSectionComponent = sectionComponents[activeSection];

  // Handle section transitions with smooth animations
  const handleSectionChange = (newSection: keyof typeof sectionComponents) => {
    if (newSection === activeSection) return;
    
    setIsTransitioning(true);
    
    // Small delay to allow fade out animation
    setTimeout(() => {
      setActiveSection(newSection);
      setIsTransitioning(false);
    }, 150);
  };

  // Reset transition state when component mounts
  useEffect(() => {
    setIsTransitioning(false);
  }, []);

  return (
    <div className="dark min-h-screen bg-black text-gray-300 antialiased relative overflow-hidden">
      {/* Sophisticated Background Effects */}
      <div className="absolute inset-0 pointer-events-none">
        {/* Premium Central Gradient */}
        <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[1000px] h-[1000px] bg-gradient-to-br from-zinc-800/20 via-slate-800/15 to-gray-800/20 rounded-full blur-[400px] animate-pulse-slow opacity-80" />
        
        {/* Elegant Floating Orbs */}
        <div className="absolute top-16 left-16 w-80 h-80 bg-gradient-to-br from-zinc-700/25 to-transparent rounded-full blur-3xl animate-float opacity-60" />
        <div className="absolute bottom-16 right-16 w-96 h-96 bg-gradient-to-tl from-slate-700/30 to-transparent rounded-full blur-3xl animate-float animation-delay-3000 opacity-70" />
        <div className="absolute top-32 right-1/3 w-64 h-64 bg-gradient-to-bl from-gray-700/20 to-transparent rounded-full blur-2xl animate-float animation-delay-1500 opacity-50" />
        <div className="absolute bottom-32 left-1/3 w-72 h-72 bg-gradient-to-tr from-zinc-600/25 to-transparent rounded-full blur-3xl animate-float animation-delay-4000 opacity-65" />
        
        {/* Premium Grid Pattern */}
        <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,.008)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,.008)_1px,transparent_1px)] bg-[size:100px_100px] opacity-40" />
        
        {/* Sophisticated Gradient Overlays */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-black/10" />
        <div className="absolute inset-0 bg-gradient-to-b from-zinc-900/5 via-transparent to-slate-900/5" />
      </div>
      
      <SettingsPageHeader onBack={onBack} />
      
      <main className="relative z-10 flex min-h-[calc(100vh-4rem)] flex-1 flex-col gap-4 p-4 md:gap-8 md:p-6 lg:p-10">
        {/* Enhanced header section */}
        <div className="mx-auto grid w-full max-w-6xl gap-6">
          <div className="space-y-4">
            <div className="flex justify-center mb-6">
              <div className="flex items-center space-x-2 bg-zinc-900/50 backdrop-blur-sm border border-zinc-800/50 rounded-full px-4 py-2">
                <div className="w-2 h-2 bg-zinc-400/60 rounded-full animate-pulse" />
                <span className="text-xs text-zinc-400 font-medium tracking-wide uppercase">Configuration</span>
              </div>
            </div>
            <h1 className="text-4xl md:text-6xl font-extrabold text-center tracking-tight">
              <span className="bg-gradient-to-br from-white via-zinc-100 to-zinc-300 bg-clip-text text-transparent">
                Settings
              </span>
            </h1>
            <p className="text-xl text-zinc-400 max-w-3xl mx-auto leading-relaxed text-center">
              Customize your AI experience, manage preferences, and configure integrations
            </p>
          </div>
        </div>

        {/* Main content grid with enhanced responsive behavior */}
        <div className="mx-auto grid w-full max-w-6xl items-start gap-8 md:grid-cols-[280px_1fr] xl:gap-12">
          {/* Enhanced sidebar navigation */}
          <nav className="sticky top-8">
            <div className="relative group">
              {/* Glow Effect */}
              <div className="absolute -inset-1 bg-gradient-to-r from-zinc-600/0 via-zinc-500/20 to-zinc-600/0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-sm" />
              
              {/* Main Sidebar Container */}
              <div className="relative bg-gradient-to-br from-zinc-900/60 via-slate-900/40 to-zinc-900/60 backdrop-blur-md border border-zinc-800/60 rounded-2xl p-1 transition-all duration-300 hover:border-zinc-700/80">
                <SettingsSidebar
                  activeSection={activeSection}
                  onSectionChange={handleSectionChange}
                />
              </div>
            </div>
          </nav>

          {/* Enhanced content area with smooth transitions */}
          <div className="relative min-h-[600px]">
            <div 
              className={`transition-all duration-300 ease-in-out ${
                isTransitioning 
                  ? 'opacity-0 translate-y-2 scale-[0.98]' 
                  : 'opacity-100 translate-y-0 scale-100'
              }`}
            >
              <Suspense 
                fallback={
                  <div className="relative group">
                    {/* Glow Effect */}
                    <div className="absolute -inset-1 bg-gradient-to-r from-zinc-600/0 via-zinc-500/10 to-zinc-600/0 rounded-3xl opacity-100 blur-sm" />
                    {/* Main Content Container */}
                    <div className="relative bg-gradient-to-br from-zinc-900/60 via-slate-900/30 to-zinc-900/60 backdrop-blur-md border border-zinc-800/60 rounded-2xl p-8">
                      <SettingsSkeleton 
                        variant={sectionSkeletonVariants[activeSection] || "simple"}
                        className="animate-in fade-in-0 duration-300"
                      />
                    </div>
                  </div>
                }
              >
                {ActiveSectionComponent && (
                  <div className="animate-in fade-in-0 slide-in-from-bottom-2 duration-500 ease-out">
                    <div className="relative group">
                      {/* Glow Effect */}
                      <div className="absolute -inset-1 bg-gradient-to-r from-zinc-600/0 via-zinc-500/10 to-zinc-600/0 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 blur-sm" />
                      {/* Main Content Container */}
                      <div className="relative bg-gradient-to-br from-zinc-900/60 via-slate-900/30 to-zinc-900/60 backdrop-blur-md border border-zinc-800/60 rounded-2xl p-8 transition-all duration-300 hover:border-zinc-700/80">
                        <ActiveSectionComponent />
                      </div>
                    </div>
                  </div>
                )}
              </Suspense>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
