import * as Sentry from "@sentry/react";
import { createRoot } from "react-dom/client";
import { ConvexAuthProvider } from "@convex-dev/auth/react";
import { ConvexReactClient } from "convex/react";
import "./indexv2.css";
import App from "./App";
import { ThemeProvider } from "./components/ThemeProvider";

Sentry.init({
  dsn: "https://<EMAIL>/4509799475380224",
  sendDefaultPii: true,
});

const convex = new ConvexReactClient(import.meta.env.VITE_CONVEX_URL as string);

createRoot(document.getElementById("root")!).render(
  <ConvexAuthProvider client={convex}>
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      storageKey="vite-ui-theme"
    >
      <App />
    </ThemeProvider>
  </ConvexAuthProvider>,
);
