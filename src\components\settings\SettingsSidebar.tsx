import {
  User,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  KeyRound,
  BarChart4,
  Database,
  Server,
  Workflow,
  Settings,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";

export const navItems = [
  { id: "account", label: "Account", icon: User },
  { id: "appearance", label: "Appearance", icon: Palette },
  { id: "ai", label: "AI", icon: Bo<PERSON> },
  { id: "apiKeys", label: "API Keys", icon: KeyRound },
  { id: "customProviders", label: "Custom Providers", icon: Settings },
  { id: "data", label: "Data", icon: Database },
  { id: "mcp", label: "MCP", icon: Server },
  { id: "n8n", label: "n8n", icon: Workflow },
  { id: "usage", label: "Usage", icon: BarChart4 },
];

interface SettingsSidebarProps {
  activeSection: string;
  onSectionChange: (section: string) => void;
  className?: string;
}

export function SettingsSidebar({
  activeSection,
  onSectionChange,
  className,
}: SettingsSidebarProps) {
  return (
    <nav className={cn("flex flex-col gap-2 p-4", className)}>
      {navItems.map((item, index) => {
        const isActive = activeSection === item.id;
        return (
          <div key={item.id} className="relative group">
            {/* Hover glow effect */}
            <div className={`absolute -inset-1 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm ${
              isActive 
                ? 'bg-gradient-to-r from-zinc-500/20 via-zinc-400/20 to-zinc-500/20' 
                : 'bg-gradient-to-r from-zinc-600/0 via-zinc-500/10 to-zinc-600/0'
            }`} />
            
            {/* Main button */}
            <Button
              variant="ghost"
              className={cn(
                "relative w-full justify-start gap-3 py-3 px-4 text-sm font-medium transition-all duration-300 rounded-xl border backdrop-blur-sm group-hover:transform group-hover:scale-[1.02]",
                isActive 
                  ? "bg-gradient-to-r from-zinc-800/80 to-slate-800/60 border-zinc-700/70 text-white shadow-lg" 
                  : "bg-gradient-to-r from-zinc-900/40 to-zinc-800/20 border-zinc-800/40 text-zinc-300 hover:border-zinc-700/60 hover:bg-gradient-to-r hover:from-zinc-900/60 hover:to-zinc-800/40 hover:text-white"
              )}
              onClick={() => onSectionChange(item.id)}
            >
              {/* Status indicator */}
              <div className="flex items-center justify-between w-full">
                <div className="flex items-center gap-3">
                  <div className={`w-1.5 h-1.5 rounded-full transition-colors duration-300 ${
                    isActive ? 'bg-zinc-400/80' : 'bg-zinc-600/60 group-hover:bg-zinc-500/80'
                  }`} />
                  <item.icon className={cn(
                    "size-5 shrink-0 transition-colors duration-300",
                    isActive ? "text-zinc-200" : "text-zinc-400 group-hover:text-zinc-300"
                  )} />
                  <span className="transition-colors duration-300">{item.label}</span>
                </div>
                <div className={`text-xs font-mono transition-colors duration-300 ${
                  isActive ? 'text-zinc-500' : 'text-zinc-600 group-hover:text-zinc-500'
                }`}>
                  {String(index + 1).padStart(2, '0')}
                </div>
              </div>
            </Button>
          </div>
        );
      })}
    </nav>
  );
} 