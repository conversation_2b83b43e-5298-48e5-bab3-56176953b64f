import { httpRouter } from "convex/server";
import { auth } from "./auth";
import { oauthCallback } from "./github_oauth";
import { stripeWebhookWrapper } from "./stripeWebhookWrapper";
import { httpAction } from "./_generated/server";
import { internal } from "./_generated/api";

const http = httpRouter();

// GitHub OAuth redirect URI
http.route({
  path: "/github/oauth/callback",
  method: "GET",
  handler: oauthCallback,
});

// Stripe webhook endpoint (POST for actual webhooks)
http.route({
  path: "/webhooks/stripe",
  method: "POST",
  handler: stripeWebhookWrapper,
});

// Test endpoint to verify webhook is working (separate path)
http.route({
  path: "/webhooks/stripe/test",
  method: "GET",
  handler: httpAction(async () => {
    return new Response("Stripe webhook endpoint is configured and ready!", {
      status: 200,
      headers: { "Content-Type": "text/plain" },
    });
  }),
});

// Resend webhook endpoint for email event tracking
http.route({
  path: "/webhooks/resend",
  method: "POST",
  handler: httpAction(async (ctx, request) => {
    try {
      const body = await request.json();
      console.log("Resend webhook received:", body);
      
      // Process the webhook event
      if (body.type && body.data) {
        await ctx.runMutation(internal.emailEvents.handleEmailEvent, {
          id: body.data.id || "unknown",
          event: {
            type: body.type,
            createdAt: body.created_at || new Date().toISOString(),
            data: body.data,
          },
        });
      }
      
      return new Response("OK", { status: 200 });
    } catch (error) {
      console.error("Resend webhook error:", error);
      return new Response("Error processing webhook", { status: 500 });
    }
  }),
});

// Test endpoint for Resend webhook
http.route({
  path: "/webhooks/resend/test",
  method: "GET",
  handler: httpAction(async () => {
    return new Response("Resend webhook endpoint is configured and ready!", {
      status: 200,
      headers: { "Content-Type": "text/plain" },
    });
  }),
});

auth.addHttpRoutes(http);

export default http;
