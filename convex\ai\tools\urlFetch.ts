"use node";

import { tool } from "ai";
import { z } from "zod";
import { browseUrl } from "../utils/browse";
import { api } from "../../_generated/api";
import { jsonSchema } from "ai";

export function createUrlFetchTool(ctx: any) {
  return tool({
    description: `Retrieve **detailed content** from a single web page (HTML, text, metadata) using the Instant Search API.

When to use:
• You already know the exact URL (for example, the user pasted it or you discovered it via web_search) **and** you need more than the snippet.
• You want to summarise, extract key facts, or quote text from that page.

When *not* to use:
• For broad discovery or searching – use \`web_search\` first.
• When you can confidently answer without reading the page.

Parameters:
• \`url\` – the absolute URL to fetch.
• \`analysis_type\` – optional: "content" (default, full cleaned article), "summary" (short summary), "extract" (structured key facts), or "metadata" (title, description, etc.).

Be mindful of user-disabled tools: only call this if \`url_fetch\` is enabled.`,
    inputSchema: jsonSchema({
      type: "object",
      properties: {
        url: {
          type: "string",
          description: "The specific URL to fetch and analyze content from",
        },
        analysis_type: {
          type: "string",
          enum: ["content", "summary", "extract", "metadata"],
          description:
            "Type of analysis to perform on the URL content. Use 'content' for default analysis.",
        },
      },
      required: ["url", "analysis_type"],
      additionalProperties: false,
    }),
    execute: async (args: any) => {
      const { url, analysis_type = "content" } = args;

      // Handle empty URL
      if (!url || url.trim() === "") {
        return `Error: No URL provided for fetching.

Please provide a valid URL such as:
- https://example.com/article
- https://github.com/user/repo
- https://docs.example.com/guide`;
      }

      // Validate URL format
      try {
        new URL(url.trim());
      } catch {
        return `Error: Invalid URL format. Please provide a complete URL starting with http:// or https://`;
      }

      const actualAnalysisType = analysis_type || "content";
      // Get Instant API key from the context
      const apiKeyRecord = await ctx.runQuery(api.apiKeys.getByProvider, {
        provider: "instant",
      });

      let apiKey = "";
      let usingUserKey = false;

      // PRIORITIZE USER'S API KEY FIRST
      if (apiKeyRecord?.apiKey && apiKeyRecord.apiKey.trim().length > 0) {
        apiKey = apiKeyRecord.apiKey.trim();
        usingUserKey = true;
      } else {
        // Use built-in Instant key as fallback
        apiKey = process.env.INSTANT_API_KEY || "";
      }

      if (!apiKey) {
        return `Error: Instant API key not configured. Please add your Instant API key in settings to use URL fetching, or set INSTANT_API_KEY environment variable.`;
      }

      return await browseUrl(url.trim(), actualAnalysisType, apiKey);
    },
  });
}
