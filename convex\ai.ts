"use node";

import { action } from "./_generated/server";
import { api } from "./_generated/api";
import { v } from "convex/values";
import {
  generateStreamingResponse as streamingHandler,
  generateNonStreamingResponse as nonStreamingHandler,
  generate<PERSON><PERSON><PERSON> as titleHandler,
  getAvailableProviders as providersHandler,
} from "./ai/core";
import { getAvailableToolsConfig } from "./ai/tools";
import { PROVIDER_CONFIGS } from "../src/lib/models";
import { PROVIDER_BASE_URLS } from "./ai/providers/constants";
import { createMCPTools } from "./ai/tools";

export const maxDuration = 1600;

// Define the message content type to match the handlers
const messageContentSchema = v.union(
  v.string(),
  v.array(
    v.union(
      v.object({
        type: v.literal("text"),
        text: v.string(),
      }),
      v.object({
        type: v.literal("image"),
        image: v.string(),
        mediaType: v.optional(v.string()),
      }),
      v.object({
        type: v.literal("file"),
        data: v.string(),
        mediaType: v.optional(v.string()),
      })
    )
  )
);

export const generateStreamingResponse = action({
  args: {
    conversationId: v.id("conversations"),
    branchId: v.optional(v.string()),
    messages: v.array(
      v.object({
        role: v.string(),
        content: messageContentSchema,
        toolCalls: v.optional(
          v.array(
            v.object({
              id: v.string(),
              name: v.string(),
              arguments: v.string(),
              result: v.optional(v.string()),
              startTime: v.optional(v.number()),
              endTime: v.optional(v.number()),
            })
          )
        ),
      })
    ),
    provider: v.optional(v.string()),
    model: v.optional(v.string()),
    temperature: v.optional(v.number()),
    enabledTools: v.optional(v.array(v.string())),
    thinkingBudget: v.optional(v.union(v.string(), v.number())),
    persona: v.optional(v.string()),
    recipe: v.optional(v.string()),
  },
  handler: streamingHandler,
});

export const generateResponse = action({
  args: {
    conversationId: v.id("conversations"),
    branchId: v.optional(v.string()),
    messages: v.array(
      v.object({
        role: v.string(),
        content: messageContentSchema,
        toolCalls: v.optional(
          v.array(
            v.object({
              id: v.string(),
              name: v.string(),
              arguments: v.string(),
              result: v.optional(v.string()),
              startTime: v.optional(v.number()),
              endTime: v.optional(v.number()),
            })
          )
        ),
      })
    ),
    provider: v.optional(v.string()),
    model: v.optional(v.string()),
    temperature: v.optional(v.number()),
    enabledTools: v.optional(v.array(v.string())),
    thinkingBudget: v.optional(v.union(v.string(), v.number())),
    persona: v.optional(v.string()),
    recipe: v.optional(v.string()),
  },
  handler: nonStreamingHandler,
});

export const getProviderModels = action({
  args: {
    provider: v.union(
      v.literal("openai"),
      v.literal("anthropic"),
      v.literal("google"),
      v.literal("openrouter"),
      v.literal("groq"),
      v.literal("deepseek"),
      v.literal("grok"),
      v.literal("cohere"),
      v.literal("mistral"),
      v.literal("cerebras"),
      v.literal("github")
    ),
  },
  handler: async (_ctx, args) => {
    const cfg = (PROVIDER_CONFIGS as Record<string, any>)[args.provider] ?? {
      models: [],
    };
    return {
      name: args.provider,
      models: cfg.models ?? [],
      baseURL: PROVIDER_BASE_URLS[args.provider],
    };
  },
});

export const getCustomProviderModels = action({
  args: {
    providerName: v.string(),
  },
  returns: v.object({
    name: v.string(),
    displayName: v.string(),
    models: v.array(v.string()),
    baseURL: v.string(),
  }),
  handler: async (ctx, args) => {
    const customProviders: any[] = await ctx.runQuery(api.customProviders.list);
    const customProvider: any = customProviders.find(
      (cp: any) => cp.name === args.providerName
    );

    if (!customProvider) {
      throw new Error(`Custom provider '${args.providerName}' not found`);
    }

    return {
      name: customProvider.name,
      displayName: customProvider.displayName,
      models: customProvider.models,
      baseURL: customProvider.baseURL,
    };
  },
});

export const getAvailableProviders = action({
  args: {},
  handler: providersHandler,
});

export const generateTitle = action({
  args: {
    userMessage: v.string(),
  },
  handler: async (ctx, args) => {
    return await titleHandler(ctx, args.userMessage);
  },
});

export const getAvailableTools = action({
  args: {},
  handler: async (ctx, _args) => {
    // Get enabled MCP servers
    let mcpServers: any[] = [];
    try {
      mcpServers = await ctx.runQuery(api.mcpServers.listEnabled);
    } catch (error) {
      console.error("Failed to load MCP servers:", error);
      // Continue without MCP servers if not authenticated or error occurs
    }

    // Get enabled n8n servers
    let n8nServers: any[] = [];
    try {
      n8nServers = await ctx.runQuery(api.n8nServers.listEnabled);
    } catch (error) {
      console.error("Failed to load n8n servers:", error);
      // Continue without n8n servers if not authenticated or error occurs
    }

    // Legacy: load enabled n8n workflows (if workflow-based approach still present)
    let n8nWorkflows: any[] = [];
    try {
      n8nWorkflows = await ctx.runQuery(api.n8nWorkflows.listEnabled);
    } catch (error) {
      // Ignore if table removed
    }

    // Return base tool configuration including MCP and n8n server tools
    return getAvailableToolsConfig(mcpServers, n8nServers, n8nWorkflows);
  },
});

// Prefetch MCP tools to warm cache and avoid first-message latency
export const prefetchMCPTools = action({
  args: {},
  returns: v.null(),
  handler: async (ctx) => {
    try {
      const servers: any[] = await ctx.runQuery(api.mcpServers.listEnabled);
      if (servers && servers.length > 0) {
        await createMCPTools(servers);
      }
    } catch (err) {
      console.error("MCP prefetch failed (non-fatal):", err);
    }
    return null;
  },
});
