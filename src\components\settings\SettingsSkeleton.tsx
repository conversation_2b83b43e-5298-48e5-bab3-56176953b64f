import { cn } from "@/lib/utils";

interface SkeletonProps {
  className?: string;
}

function Skeleton({ className, ...props }: SkeletonProps & React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      className={cn(
        "animate-pulse rounded-md bg-zinc-800/40 relative overflow-hidden",
        "before:absolute before:inset-0 before:-translate-x-full before:animate-shimmer",
        "before:bg-gradient-to-r before:from-transparent before:via-zinc-600/20 before:to-transparent",
        className
      )}
      {...props}
    />
  );
}

interface SettingsSkeletonProps {
  variant?: "simple" | "complex" | "list" | "usage" | "grid";
  className?: string;
}

export function SettingsSkeleton({ variant = "simple", className }: SettingsSkeletonProps) {
  const baseCardClasses = "space-y-6 p-8 bg-gradient-to-br from-zinc-900/60 via-slate-900/30 to-zinc-900/60 backdrop-blur-md border border-zinc-800/60 rounded-2xl shadow-xl";

  if (variant === "simple") {
    return (
      <div className={cn(baseCardClasses, className)}>
        {/* Header with status indicator */}
        <div className="space-y-4">
          <div className="flex items-center justify-between mb-4">
            <div className="w-2 h-2 bg-zinc-500/40 rounded-full animate-pulse" />
            <div className="text-xs text-zinc-600 font-mono">01</div>
          </div>
          <Skeleton className="h-6 w-48" />
          <Skeleton className="h-4 w-96" />
        </div>
        
        {/* Content */}
        <div className="space-y-6">
          <div className="space-y-3">
            <Skeleton className="h-4 w-32" />
            <Skeleton className="h-10 w-full rounded-xl" />
          </div>
          <div className="space-y-3">
            <Skeleton className="h-4 w-40" />
            <Skeleton className="h-10 w-full rounded-xl" />
          </div>
          <div className="flex gap-3 pt-2">
            <Skeleton className="h-10 w-24 rounded-xl" />
            <Skeleton className="h-10 w-28 rounded-xl" />
          </div>
        </div>
      </div>
    );
  }

  if (variant === "complex") {
    return (
      <div className={cn(baseCardClasses, className)}>
        {/* Header with badge */}
        <div className="space-y-4">
          <div className="flex items-center justify-between mb-4">
            <div className="w-2 h-2 bg-zinc-500/40 rounded-full animate-pulse" />
            <div className="text-xs text-zinc-600 font-mono">01</div>
          </div>
          <div className="flex items-center justify-between">
            <Skeleton className="h-6 w-48" />
            <Skeleton className="h-6 w-20 rounded-full" />
          </div>
          <Skeleton className="h-4 w-80" />
        </div>
        
        {/* Multiple sections */}
        <div className="space-y-8">
          {/* Section 1 */}
          <div className="space-y-4">
            <Skeleton className="h-5 w-36" />
            <div className="space-y-3">
              <Skeleton className="h-4 w-full rounded-lg" />
              <Skeleton className="h-2 w-full rounded-full" />
              <Skeleton className="h-3 w-64" />
            </div>
          </div>
          
          {/* Separator */}
          <Skeleton className="h-px w-full bg-zinc-700/50" />
          
          {/* Section 2 */}
          <div className="space-y-4">
            <Skeleton className="h-5 w-44" />
            <div className="flex justify-between items-center p-4 bg-zinc-800/20 rounded-xl border border-zinc-700/30">
              <div className="space-y-2">
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-3 w-48" />
              </div>
              <div className="flex gap-3">
                <Skeleton className="h-9 w-20 rounded-xl" />
                <Skeleton className="h-9 w-28 rounded-xl" />
                <Skeleton className="h-9 w-24 rounded-xl" />
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (variant === "list") {
    return (
      <div className={cn(baseCardClasses, className)}>
        {/* Header */}
        <div className="space-y-4">
          <div className="flex items-center justify-between mb-4">
            <div className="w-2 h-2 bg-zinc-500/40 rounded-full animate-pulse" />
            <div className="text-xs text-zinc-600 font-mono">01</div>
          </div>
          <Skeleton className="h-6 w-40" />
          <Skeleton className="h-4 w-72" />
        </div>
        
        {/* List items */}
        <div className="space-y-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <div key={i} className="relative group">
              <div className="absolute -inset-1 bg-gradient-to-r from-zinc-600/0 via-zinc-500/5 to-zinc-600/0 rounded-xl opacity-100 blur-sm" />
              <div className="relative bg-gradient-to-r from-zinc-900/40 to-zinc-800/20 border border-zinc-800/40 rounded-xl p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-1.5 h-1.5 bg-zinc-500/40 rounded-full" />
                    <Skeleton className="h-8 w-8 rounded-lg" />
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-32" />
                      <Skeleton className="h-3 w-48" />
                    </div>
                  </div>
                  <div className="flex gap-3">
                    <Skeleton className="h-9 w-20 rounded-xl" />
                    <Skeleton className="h-9 w-9 rounded-xl" />
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        {/* Add button */}
        <Skeleton className="h-10 w-36 rounded-xl" />
      </div>
    );
  }

  if (variant === "usage") {
    return (
      <div className={cn(baseCardClasses, className)}>
        {/* Header with badge */}
        <div className="space-y-4">
          <div className="flex items-center justify-between mb-4">
            <div className="w-2 h-2 bg-zinc-500/40 rounded-full animate-pulse" />
            <div className="text-xs text-zinc-600 font-mono">01</div>
          </div>
          <div className="flex items-center justify-between">
            <Skeleton className="h-6 w-44" />
            <Skeleton className="h-6 w-24 rounded-full" />
          </div>
          <Skeleton className="h-4 w-96" />
        </div>
        
        {/* Usage progress section */}
        <div className="space-y-6">
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-32" />
            </div>
            <Skeleton className="h-3 w-full rounded-full" />
            <div className="flex justify-between items-center">
              <Skeleton className="h-3 w-40" />
            </div>
            <Skeleton className="h-3 w-48" />
          </div>
          
          {/* Separator */}
          <Skeleton className="h-px w-full bg-zinc-700/50" />
          
          {/* Subscription section */}
          <div className="space-y-6">
            <div className="flex justify-between items-center p-4 bg-zinc-800/20 rounded-xl border border-zinc-700/30">
              <div className="space-y-2">
                <Skeleton className="h-4 w-36" />
                <Skeleton className="h-3 w-48" />
              </div>
              <div className="flex gap-3">
                <Skeleton className="h-9 w-20 rounded-xl" />
                <Skeleton className="h-9 w-28 rounded-xl" />
                <Skeleton className="h-9 w-24 rounded-xl" />
              </div>
            </div>
            
            {/* Plan cards grid */}
            <div className="space-y-4">
              <Skeleton className="h-4 w-40" />
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {Array.from({ length: 3 }).map((_, i) => (
                  <div key={i} className="relative group">
                    <div className="absolute -inset-1 bg-gradient-to-r from-zinc-600/0 via-zinc-500/5 to-zinc-600/0 rounded-xl opacity-100 blur-sm" />
                    <div className="relative bg-gradient-to-r from-zinc-900/40 to-zinc-800/20 border border-zinc-800/40 rounded-xl p-4 space-y-4">
                      <Skeleton className="h-4 w-16 mx-auto" />
                      <Skeleton className="h-6 w-20 mx-auto" />
                      <div className="space-y-2">
                        <Skeleton className="h-3 w-24 mx-auto" />
                        <Skeleton className="h-3 w-20 mx-auto" />
                      </div>
                      <Skeleton className="h-9 w-full rounded-xl" />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (variant === "grid") {
    return (
      <div className={cn(baseCardClasses, className)}>
        {/* Header */}
        <div className="space-y-4">
          <div className="flex items-center justify-between mb-4">
            <div className="w-2 h-2 bg-zinc-500/40 rounded-full animate-pulse" />
            <div className="text-xs text-zinc-600 font-mono">01</div>
          </div>
          <Skeleton className="h-6 w-48" />
          <Skeleton className="h-4 w-80" />
        </div>
        
        {/* Grid content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, i) => (
            <div key={i} className="relative group">
              <div className="absolute -inset-1 bg-gradient-to-r from-zinc-600/0 via-zinc-500/5 to-zinc-600/0 rounded-xl opacity-100 blur-sm" />
              <div className="relative bg-gradient-to-r from-zinc-900/40 to-zinc-800/20 border border-zinc-800/40 rounded-xl p-4 space-y-4">
                <div className="flex items-center gap-3">
                  <div className="w-1.5 h-1.5 bg-zinc-500/40 rounded-full" />
                  <Skeleton className="h-10 w-10 rounded-lg" />
                  <div className="space-y-2 flex-1">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-3 w-32" />
                  </div>
                </div>
                <Skeleton className="h-9 w-full rounded-xl" />
              </div>
            </div>
          ))}
        </div>
        
        {/* Action button */}
        <div className="flex justify-center pt-4">
          <Skeleton className="h-10 w-44 rounded-xl" />
        </div>
      </div>
    );
  }

  // Default simple variant
  return (
    <div className={cn(baseCardClasses, className)}>
      <div className="space-y-4">
        <div className="flex items-center justify-between mb-4">
          <div className="w-2 h-2 bg-zinc-500/40 rounded-full animate-pulse" />
          <div className="text-xs text-zinc-600 font-mono">01</div>
        </div>
        <Skeleton className="h-6 w-48" />
        <Skeleton className="h-4 w-96" />
      </div>
      <div className="space-y-6">
        <Skeleton className="h-10 w-full rounded-xl" />
        <Skeleton className="h-10 w-full rounded-xl" />
        <div className="flex gap-3">
          <Skeleton className="h-10 w-24 rounded-xl" />
          <Skeleton className="h-10 w-28 rounded-xl" />
        </div>
      </div>
    </div>
  );
}

// Export individual skeleton components for specific use cases
export function SimpleSettingsSkeleton({ className }: { className?: string }) {
  return <SettingsSkeleton variant="simple" className={className} />;
}

export function ComplexSettingsSkeleton({ className }: { className?: string }) {
  return <SettingsSkeleton variant="complex" className={className} />;
}

export function ListSettingsSkeleton({ className }: { className?: string }) {
  return <SettingsSkeleton variant="list" className={className} />;
}

export function UsageSettingsSkeleton({ className }: { className?: string }) {
  return <SettingsSkeleton variant="usage" className={className} />;
}

export function GridSettingsSkeleton({ className }: { className?: string }) {
  return <SettingsSkeleton variant="grid" className={className} />;
}