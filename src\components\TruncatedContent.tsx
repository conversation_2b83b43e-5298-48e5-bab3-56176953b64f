"use client";

import React, { memo, useState, useMemo } from "react";
import { ChevronDown, ChevronUp } from "lucide-react";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "./ui/collapsible";

interface TruncatedContentProps {
  content: string;
  maxLines?: number;
  className?: string;
}

const TruncatedContent: React.FC<TruncatedContentProps> = memo(({
  content,
  maxLines = 4,
  className = "",
}) => {
  const [isOpen, setIsOpen] = useState(false);

  // Memoize the line clamp class to avoid recalculation
  const lineClampClass = useMemo(() => {
    const clampMap: Record<number, string> = {
      1: "line-clamp-1",
      2: "line-clamp-2",
      3: "line-clamp-3",
      4: "line-clamp-4",
      5: "line-clamp-5",
      6: "line-clamp-6",
    };
    return clampMap[maxLines] || `line-clamp-${maxLines}`;
  }, [maxLines]);

  // Check if content is long enough to warrant truncation
  // Simple heuristic: if content has more than maxLines * 80 characters or multiple line breaks
  const shouldTruncate = useMemo(() => {
    const lineBreaks = (content.match(/\n/g) || []).length;
    const estimatedLines = Math.ceil(content.length / 80) + lineBreaks;
    return estimatedLines > maxLines;
  }, [content, maxLines]);

  if (!shouldTruncate) {
    return (
      <div className={className}>
        {content}
      </div>
    );
  }

  return (
    <Collapsible open={isOpen} onOpenChange={setIsOpen} className={className}>
      <div className="relative">
        {/* Truncated content with gradient overlay when collapsed */}
        {!isOpen && (
          <div className="relative">
            <div className={`${lineClampClass} whitespace-pre-wrap`}>
              {content}
            </div>
            {/* Gradient fade-out overlay */}
            <div className="absolute bottom-0 left-0 right-0 h-6 bg-gradient-to-t from-background via-background/80 to-transparent pointer-events-none" />
          </div>
        )}

        {/* Full content when expanded */}
        <CollapsibleContent className="data-[state=closed]:hidden">
          <div className="whitespace-pre-wrap">
            {content}
          </div>
        </CollapsibleContent>

        {/* Toggle button */}
        <CollapsibleTrigger className="flex items-center gap-1 mt-2 text-sm font-medium text-primary-foreground/80 hover:text-primary-foreground transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 rounded-sm">
          {isOpen ? (
            <>
              <span>Show less</span>
              <ChevronUp className="h-3 w-3" />
            </>
          ) : (
            <>
              <span>Show more</span>
              <ChevronDown className="h-3 w-3" />
            </>
          )}
        </CollapsibleTrigger>
      </div>
    </Collapsible>
  );
});

TruncatedContent.displayName = "TruncatedContent";

export default TruncatedContent;