"use node";

export async function browseUrl(
  url: string,
  analysisType: string = "content",
  instantApiKey?: string
): Promise<string> {
  try {
    // Validate URL format
    try {
      new URL(url);
    } catch {
      return `Error: Invalid URL format "${url}"`;
    }

    // Check if Instant API key is provided
    if (!instantApiKey) {
      return `Error: Instant API key is required to fetch URL content. Please configure your Instant API key in settings or set INSTANT_API_KEY environment variable.`;
    }

    // Decide which endpoint to hit. Use /quick for lightweight summary requests.
    const useQuickEndpoint = analysisType === "summary";
    const endpoint = `https://instant.erzen.tk/api/contents${useQuickEndpoint ? "/quick" : ""}`;

    const response = await fetch(endpoint, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${instantApiKey}`,
      },
      body: JSON.stringify({ urls: [url] }),
      signal: AbortSignal.timeout(30000), // 30 s timeout
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error("[INSTANT DEBUG] API error:", response.status, errorText);
      return `Error fetching URL "${url}" with Instant API: ${response.status} ${response.statusText}. ${errorText}`;
    }

    const data = await response.json();

    // Try common response shapes
    // The API might return an array, an object with a `contents` array, or the content directly.
    const item: any = Array.isArray(data)
      ? data[0]
      : (data?.contents?.[0] ?? data?.result ?? data);

    if (!item) {
      return `Content from ${url}: No content found or empty response from Instant API.`;
    }

    // Attempt to pick the most useful text field.
    let content: string =
      item.text ||
      item.content ||
      item.markdown ||
      item.html ||
      JSON.stringify(item, null, 2);

    if (!content || content.trim().length === 0) {
      return `Content from ${url}: No content found or empty response from Instant API.`;
    }

    const maxLength = analysisType === "summary" ? 3000 : 5000;
    if (content.length > maxLength) {
      content = content.substring(0, maxLength) + "...";
    }

    return `Content from ${url} (extracted with Instant API):\n\n${content}`;
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error";
    console.error("[INSTANT DEBUG] Error:", errorMessage);
    return `Error fetching URL "${url}" with Instant API: ${errorMessage}`;
  }
}
