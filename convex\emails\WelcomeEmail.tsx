import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  <PERSON>,
  Html,
  <PERSON><PERSON>,
  <PERSON>,
  Section,
  Text,
} from "@react-email/components";
import React from "react";

interface WelcomeEmailProps {
  userEmail: string;
  verificationUrl: string;
}

export const WelcomeEmail = ({
  userEmail = "<EMAIL>",
  verificationUrl = "https://ai.erzen.tk/verify",
}: WelcomeEmailProps) => {
  return (
    <Html>
      <Head />
      <Body style={main}>
        <Container style={container}>
          <Section style={header}>
            <Text style={headerText}>Welcome to Erzen AI</Text>
          </Section>
          
          <Section style={content}>
            <Text style={greeting}>Welcome to the future of AI</Text>
            
            <Text style={paragraph}>
              You've joined Erzen AI, the most advanced AI workspace designed for creators, 
              developers, and teams. To begin building with 75+ AI models and unlock 
              autonomous capabilities, please verify your email address.
            </Text>
            
            <Section style={callout}>
              <Text style={calloutText}>
                Verify your email to activate your account and start building
              </Text>
            </Section>
            
            <Section style={buttonContainer}>
              <Button style={button} href={verificationUrl}>
                Verify Email Address
              </Button>
            </Section>
            
            <Section style={featuresBox}>
              <Text style={featuresTitle}>Your AI capabilities include:</Text>
              <ul style={featuresList}>
                <li>Access to 75+ premium AI models</li>
                <li>Real-time streaming conversations</li>
                <li>Advanced file analysis and processing</li>
                <li>Autonomous agent execution</li>
                <li>Persistent project memory</li>
                <li>Seamless integrations and automations</li>
              </ul>
            </Section>
            
            <Hr style={hr} />
            
            <Text style={disclaimer}>
              If you didn't create an account with us, you can safely ignore this email.
            </Text>
            
            <Text style={disclaimer}>
              If the button doesn't work, copy and paste this link into your browser:
              <br />
              <Link href={verificationUrl} style={link}>
                {verificationUrl}
              </Link>
            </Text>
          </Section>
          
          <Section style={footer}>
            <Text style={footerText}>
              © 2024 Erzen AI. Conversations at the speed of thought.
            </Text>
          </Section>
        </Container>
      </Body>
    </Html>
  );
};

// Styles - Modern Dark Theme
const main = {
  backgroundColor: "#000000",
  fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
  color: "#d4d4d8",
};

const container = {
  backgroundColor: "#09090b",
  margin: "0 auto",
  padding: "0",
  marginTop: "30px",
  marginBottom: "30px",
  borderRadius: "16px",
  border: "1px solid #27272a",
  maxWidth: "600px",
  overflow: "hidden",
};

const header = {
  background: "linear-gradient(135deg, #18181b 0%, #27272a 100%)",
  padding: "40px 32px",
  textAlign: "center" as const,
  borderBottom: "1px solid #27272a",
};

const headerText = {
  color: "#ffffff",
  fontSize: "32px",
  fontWeight: "bold",
  margin: "0",
  letterSpacing: "-0.025em",
};

const content = {
  padding: "32px",
  backgroundColor: "#09090b",
};

const greeting = {
  fontSize: "20px",
  fontWeight: "600",
  color: "#ffffff",
  marginTop: "0",
  marginBottom: "24px",
};

const paragraph = {
  fontSize: "16px",
  lineHeight: "1.7",
  color: "#a1a1aa",
  marginBottom: "24px",
};

const callout = {
  backgroundColor: "#18181b",
  padding: "20px",
  borderRadius: "12px",
  border: "1px solid #27272a",
  borderLeft: "3px solid #71717a",
  margin: "24px 0",
};

const calloutText = {
  margin: "0",
  fontWeight: "600",
  color: "#e4e4e7",
  fontSize: "15px",
};

const buttonContainer = {
  textAlign: "center" as const,
  margin: "32px 0",
};

const button = {
  background: "linear-gradient(135deg, #18181b 0%, #27272a 100%)",
  borderRadius: "12px",
  color: "#ffffff",
  fontSize: "16px",
  fontWeight: "600",
  textDecoration: "none",
  textAlign: "center" as const,
  display: "inline-block",
  border: "1px solid #3f3f46",
  padding: "16px 32px",
  transition: "all 0.2s",
};

const featuresBox = {
  backgroundColor: "#18181b",
  padding: "24px",
  borderRadius: "12px",
  margin: "24px 0",
  border: "1px solid #27272a",
};

const featuresTitle = {
  marginTop: "0",
  fontSize: "16px",
  fontWeight: "600",
  color: "#ffffff",
  marginBottom: "16px",
};

const featuresList = {
  margin: "0",
  paddingLeft: "20px",
  color: "#a1a1aa",
  fontSize: "15px",
  lineHeight: "1.6",
};

const hr = {
  borderColor: "#27272a",
  margin: "32px 0",
  borderWidth: "1px",
  borderStyle: "solid",
};

const disclaimer = {
  fontSize: "14px",
  color: "#71717a",
  lineHeight: "1.6",
  marginBottom: "16px",
};

const link = {
  color: "#a1a1aa",
  textDecoration: "underline",
  wordBreak: "break-all" as const,
};

const footer = {
  textAlign: "center" as const,
  marginTop: "24px",
  padding: "24px",
  backgroundColor: "#18181b",
  borderTop: "1px solid #27272a",
};

const footerText = {
  fontSize: "12px",
  color: "#71717a",
  margin: "0",
};

export default WelcomeEmail;