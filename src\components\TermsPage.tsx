import { Button } from "./ui/button";

export function TermsPage() {
  const handleBack = () => {
    window.history.back();
  };

  return (
    <div className="py-16 px-4 max-w-3xl mx-auto">
      <h1 className="text-display-sm mb-6 bg-clip-text text-transparent bg-gradient-to-r from-primary to-accent">
        Terms & Conditions
      </h1>
      <p className="text-muted-foreground leading-relaxed mb-6">
        This is a placeholder Terms & Conditions agreement. Replace with legally binding
        terms that govern the usage of your service.
      </p>
      <h2 className="text-2xl font-bold mb-4">1. Acceptance of Terms</h2>
      <p className="text-muted-foreground mb-4">
        Outline that by using the service, users agree to these terms.
      </p>
      <h2 className="text-2xl font-bold mb-4">2. Usage Restrictions</h2>
      <p className="text-muted-foreground mb-4">
        Specify prohibited behaviors and acceptable use guidelines.
      </p>
      <h2 className="text-2xl font-bold mb-4">3. Liability</h2>
      <p className="text-muted-foreground mb-4">
        Limit your liability and disclaim warranties.
      </p>
      <Button onClick={handleBack}>Back</Button>
    </div>
  );
} 