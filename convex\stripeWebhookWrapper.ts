import { httpAction } from "./_generated/server";
import { internal } from "./_generated/api";

import { v } from "convex/values";

export const stripeWebhookWrapper = httpAction(async (ctx, request) => {
  const body = await request.text();
  const signature = request.headers.get("stripe-signature") ?? "";

  // Implement retry logic for concurrency conflicts
  let result;
  let retryCount = 0;
  const maxRetries = 3;

  while (retryCount <= maxRetries) {
    try {
      result = await ctx.runAction(internal.webhooks.processStripeEvent, {
        body,
        signature,
      });
      break; // Success, exit retry loop
    } catch (error) {
      const errorMessage = String(error);

      if (
        errorMessage.includes("OptimisticConcurrencyControlFailure") &&
        retryCount < maxRetries
      ) {
        retryCount++;
        console.log(
          `Retry attempt ${retryCount} for webhook processing due to concurrency conflict`
        );
        // Exponential backoff: 100ms, 200ms, 400ms
        await new Promise((resolve) =>
          setTimeout(resolve, 100 * Math.pow(2, retryCount - 1))
        );
        continue;
      }

      // If not a concurrency error or max retries exceeded, return the error
      console.error("Webhook processing failed after retries:", error);
      result = {
        success: false,
        message: `Webhook processing failed: ${errorMessage}`,
      };
      break;
    }
  }

  // Handle case where result is still undefined (shouldn't happen but for safety)
  if (!result) {
    result = {
      success: false,
      message: "Unknown error occurred during webhook processing",
    };
  }

  return new Response(result.message, {
    status: result.success ? 200 : 400,
    headers: {
      "Content-Type": "text/plain",
    },
  });
});

// Define the return type for the action
export type StripeWebhookResult = {
  success: boolean;
  message: string;
};
