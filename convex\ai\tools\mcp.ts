"use node";

import { tool } from "ai";
import type { Doc } from "../../_generated/dataModel";
import { JSONSchemaToZod } from "@dmitryrechkin/json-schema-to-zod";
import { z } from "zod";

// MCP client cache to avoid recreating clients
const mcpClientCache = new Map<string, any>();

// Function to create MCP client based on server configuration
export async function createMCPClient(server: Doc<"mcpServers">): Promise<any> {
  const cacheKey = `${server._id}_${server.transportType}_${server.url ?? server.command}`;

  // Return cached client if available
  if (mcpClientCache.has(cacheKey)) {
    return mcpClientCache.get(cacheKey);
  }

  let client;

  try {
    switch (server.transportType) {
      case "stdio": {
        // SECURITY: stdio transport disabled - it allows arbitrary command execution on server
        throw new Error(
          "stdio transport is coming soon, use sse, or http transport instead."
        );
      }

      case "sse": {
        if (!server.url) {
          throw new Error("URL is required for SSE transport");
        }

        // Special handling for GitHub MCP server
        const finalUrl = server.url;
        const finalHeaders = { ...server.headers };

        // Import MCP client for SSE transport
        const { Client } = await import(
          "@modelcontextprotocol/sdk/client/index.js"
        );
        const { SSEClientTransport } = await import(
          "@modelcontextprotocol/sdk/client/sse.js"
        );

        const transport = new SSEClientTransport(new URL(finalUrl));
        client = new Client(
          {
            name: `mcp-client-${server.name}`,
            version: "1.0.0",
          },
          {
            capabilities: {},
          }
        );
        await client.connect(transport);
        break;
      }

      case "http": {
        if (!server.url) {
          throw new Error("URL is required for HTTP transport");
        }

        try {
          const { StreamableHTTPClientTransport } = await import(
            "@modelcontextprotocol/sdk/client/streamableHttp.js"
          );

          const finalUrl = server.url;
          const finalHeaders = { ...server.headers };

          // Create transport with proper configuration based on Context7 docs
          const transport = new StreamableHTTPClientTransport(
            new URL(finalUrl),
            {
              requestInit: {
                headers: finalHeaders,
              },
            }
          );

          // Import MCP client for HTTP transport
          const { Client } = await import(
            "@modelcontextprotocol/sdk/client/index.js"
          );

          client = new Client(
            {
              name: `mcp-client-${server.name}`,
              version: "1.0.0",
            },
            {
              capabilities: {},
            }
          );
          await client.connect(transport);
        } catch (importError) {
          console.error(
            `Failed to create HTTP MCP client for ${server.name}:`,
            importError
          );

          // Log more details about the error
          if (importError instanceof Error) {
            console.error(`Error name: ${importError.name}`);
            console.error(`Error message: ${importError.message}`);
            console.error(`Error stack: ${importError.stack}`);
          }

          throw new Error(
            `Failed to create HTTP MCP client: ${importError instanceof Error ? importError.message : String(importError)}`
          );
        }
        break;
      }

      default: {
        throw new Error(`Unsupported transport type: ${server.transportType}`);
      }
    }

    // Cache the client
    mcpClientCache.set(cacheKey, client);

    return client;
  } catch (error) {
    console.error(`Failed to create MCP client for ${server.name}:`, error);
    throw error;
  }
}

// Helper function to clean up clients
export function clearMCPClientCache() {
  mcpClientCache.clear();
}

// Function to test MCP server connection
export async function testMCPConnection(server: Doc<"mcpServers">) {
  try {
    const client = await createMCPClient(server);
    // Try to get tools as a simple test
    const tools = await client.tools();
    return {
      success: true,
      toolCount: Object.keys(tools).length,
      message: `Connected successfully. Found ${Object.keys(tools).length} tools.`,
    };
  } catch (error) {
    return {
      success: false,
      toolCount: 0,
      message: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

// Tool definition cache with expiry
interface CachedTools {
  updatedAt: number;
  tools: Record<string, any>;
}

const toolDefinitionCache: Map<string, CachedTools> = new Map();

const TOOL_CACHE_TTL_MS = 60 * 60 * 1000; // 1 hour

// Fetch timeout and retries for robustness
const TOOL_FETCH_TIMEOUT_MS = 30000; // 30s per attempt
const TOOL_FETCH_MAX_RETRIES = 3;

// Function to get tools from an MCP server (uses cache) with retry logic
export async function getMCPTools(server: Doc<"mcpServers">) {
  const cacheKey = server._id;

  // Serve fresh cache if available
  const cached = toolDefinitionCache.get(cacheKey);
  if (cached && Date.now() - cached.updatedAt < TOOL_CACHE_TTL_MS) {
    return cached.tools;
  }

  try {
    const client = await createMCPClient(server);

    // Add timeout for tools() call
    const toolsPromise = client.tools();
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error("Tools fetch timeout")), 15000); // 15s timeout
    });

    const tools = await Promise.race([toolsPromise, timeoutPromise]);

    // Return tools with server prefix to avoid naming conflicts
    // Wrap each tool with the AI SDK's tool() helper to ensure proper schema compatibility
    const prefixedTools: Record<string, any> = {};
    Object.entries(tools).forEach(([toolName, mcpTool]) => {
      const prefixedName = `mcp_${server.name.toLowerCase().replace(/[^a-z0-9]/g, "_")}_${toolName}`;

      const parameters = (mcpTool as any)?.parameters ?? {
        type: "object",
        properties: {},
        additionalProperties: false,
      };
      if (!parameters.type) {
        parameters.type = "object";
      }
      if (!parameters.properties) {
        parameters.properties = {};
      }
      if (parameters.additionalProperties === undefined) {
        parameters.additionalProperties = false;
      }

      // Wrap execute with remapping logic for tools with exactly one required string param
      let execute =
        (mcpTool as any)?.execute ||
        (async () => "Tool execution not available");
      const required = parameters.required || [];
      const stringRequired = required.filter(
        (name: string) => parameters.properties[name]?.type === "string"
      );

      if (stringRequired.length === 1) {
        const paramName = stringRequired[0];
        const originalExecute = execute;
        execute = async (args: Record<string, any>) => {
          const fixedArgs = { ...args };
          if (
            fixedArgs[paramName] === undefined &&
            fixedArgs.query !== undefined &&
            typeof fixedArgs.query === "string"
          ) {
            fixedArgs[paramName] = fixedArgs.query;
            delete fixedArgs.query;
          }
          return await originalExecute(fixedArgs);
        };
      }

      prefixedTools[prefixedName] = tool({
        description: `[${server.name}] ${(mcpTool as any)?.description ?? toolName}`,
        inputSchema: JSONSchemaToZod.convert(
          parameters
        ) as unknown as z.ZodObject<any>,
        execute,
      });
    });

    // Save to cache
    toolDefinitionCache.set(cacheKey, {
      updatedAt: Date.now(),
      tools: prefixedTools,
    });

    return prefixedTools;
  } catch (error) {
    console.error(`Failed to get tools from MCP server ${server.name}:`, error);
    // Fallback to cache if we have one
    const fallback = toolDefinitionCache.get(server._id);
    if (fallback) {
      return fallback.tools;
    }
    // Otherwise return empty to avoid cascade failure
    return {};
  }
}

// Function to create MCP tools for all enabled servers
export async function createMCPTools(servers: Doc<"mcpServers">[]) {
  const allMCPTools: Record<string, any> = {};

  // Process servers in parallel with individual error handling
  const toolPromises = servers
    .filter((server) => server.isEnabled)
    .map(async (server) => {
      try {
        const tools = await getMCPTools(server);
        return { server, tools, success: true };
      } catch (error) {
        console.error(
          `Failed to load tools from MCP server ${server.name}:`,
          error
        );
        return { server, tools: {}, success: false };
      }
    });

  const results = await Promise.all(toolPromises);

  // Merge all tools and report results
  const successfulServers: string[] = [];
  const failedServers: string[] = [];

  results.forEach(({ server, tools, success }) => {
    Object.assign(allMCPTools, tools);
    if (success && Object.keys(tools).length > 0) {
      successfulServers.push(server.name);
    } else {
      failedServers.push(server.name);
    }
  });

  return allMCPTools;
}

// Clear cache periodically
setInterval(() => {
  const now = Date.now();
  for (const [key, cached] of toolDefinitionCache.entries()) {
    if (now - cached.updatedAt > TOOL_CACHE_TTL_MS) {
      toolDefinitionCache.delete(key);
    }
  }
}, TOOL_CACHE_TTL_MS);

// Function to get tool info for UI without creating full tools
export function getMCPToolInfo(servers: Doc<"mcpServers">[]) {
  return servers
    .filter((server) => server.isEnabled)
    .map((server) => ({
      id: `mcp_${server.name.toLowerCase().replace(/[^a-z0-9]/g, "_")}`,
      name: `MCP: ${server.name}`,
      description: server.description || `Tools from ${server.name} MCP server`,
      category: "mcp",
      mcpServerId: server._id,
      transportType: server.transportType,
      url: server.url,
      command: server.command,
      availableTools: server.availableTools || [],
    }));
}
