"use node";

import { tool } from "ai";
import { z } from "zod";
import fetch from "node-fetch";
import type { Doc } from "../../_generated/dataModel";
import { jsonSchema } from "ai";

/**
 * Generic n8n public-API tools
 *
 * These tools work against **any** n8n server. The caller supplies
 *   – apiUrl  (base URL, without trailing slash)
 *   – apiKey  (optional – leave empty for open instances)
 *
 * NOTE: n8n's public REST API is only available on paid Cloud plans or on
 * self-hosted instances where the API hasn't been disabled. If the API is
 * disabled the tools will throw a clear error.
 */

// ---------------------------------------------------------------------------
// Shared helpers
// ---------------------------------------------------------------------------

const BaseParams = z.object({
  /** Base URL of the n8n instance, e.g. https://n8n.example.com */
  apiUrl: z.union([z.string(), z.null()]),
  /** n8n API key for authentication */
  apiKey: z.union([z.string(), z.null()]),
});

function headers(apiKey?: string): Record<string, string> {
  const h: Record<string, string> = { Accept: "application/json" };
  if (apiKey) h["X-N8N-API-KEY"] = apiKey;
  return h;
}

async function apiFetch(
  baseURL: string,
  endpoint: string,
  apiKey?: string,
  options: any = {}
): Promise<any> {
  const cleanBase = baseURL.replace(/\/+$/, "");
  const url = `${cleanBase}/api/v1${endpoint}`;

  const response = await fetch(url, {
    headers: headers(apiKey),
    timeout: 30_000,
    ...options,
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`n8n API error (${response.status}): ${errorText}`);
  }

  return response.json();
}

// ---------------------------------------------------------------------------
// Core n8n API functions
// ---------------------------------------------------------------------------

async function workflowList(params: {
  apiUrl: string;
  apiKey?: string;
}): Promise<string> {
  const { apiUrl, apiKey } = params;
  if (!apiUrl) throw new Error("API URL is required");

  const result = await apiFetch(apiUrl, "/workflows", apiKey);

  if (!result?.data || !Array.isArray(result.data)) {
    return "No workflows found or invalid response format.";
  }

  const workflows = result.data.map((w: any) => ({
    id: w.id,
    name: w.name || "Untitled",
    active: w.active || false,
    tags: w.tags || [],
  }));

  return `Found ${workflows.length} workflow(s):\n${workflows
    .map(
      (w: any) =>
        `- ${w.name} (ID: ${w.id}, Active: ${w.active}) ${
          w.tags.length > 0 ? `[${w.tags.join(", ")}]` : ""
        }`
    )
    .join("\n")}`;
}

async function workflowRead(params: {
  apiUrl: string;
  apiKey?: string;
  workflowId: string;
}): Promise<string> {
  const { apiUrl, apiKey, workflowId } = params;
  if (!apiUrl) throw new Error("API URL is required");
  if (!workflowId) throw new Error("Workflow ID is required");

  const result = await apiFetch(apiUrl, `/workflows/${workflowId}`, apiKey);

  if (!result?.data) {
    return `Workflow ${workflowId} not found.`;
  }

  const workflow = result.data;
  return `Workflow Details:
- ID: ${workflow.id}
- Name: ${workflow.name || "Untitled"}
- Active: ${workflow.active || false}
- Created: ${workflow.createdAt || "N/A"}
- Updated: ${workflow.updatedAt || "N/A"}
- Nodes: ${workflow.nodes?.length || 0}
- Connections: ${workflow.connections ? Object.keys(workflow.connections).length : 0}
- Tags: ${workflow.tags?.join(", ") || "None"}`;
}

async function workflowActivate(params: {
  apiUrl: string;
  apiKey?: string;
  workflowId: string;
}): Promise<string> {
  const { apiUrl, apiKey, workflowId } = params;
  if (!apiUrl) throw new Error("API URL is required");
  if (!workflowId) throw new Error("Workflow ID is required");

  await apiFetch(apiUrl, `/workflows/${workflowId}/activate`, apiKey, {
    method: "POST",
  });

  return `Workflow ${workflowId} has been activated successfully.`;
}

async function workflowDeactivate(params: {
  apiUrl: string;
  apiKey?: string;
  workflowId: string;
}): Promise<string> {
  const { apiUrl, apiKey, workflowId } = params;
  if (!apiUrl) throw new Error("API URL is required");
  if (!workflowId) throw new Error("Workflow ID is required");

  await apiFetch(apiUrl, `/workflows/${workflowId}/deactivate`, apiKey, {
    method: "POST",
  });

  return `Workflow ${workflowId} has been deactivated successfully.`;
}

async function executionList(params: {
  apiUrl: string;
  apiKey?: string;
  limit?: number;
  workflowId?: string;
}): Promise<string> {
  const { apiUrl, apiKey, limit = 20, workflowId } = params;
  if (!apiUrl) throw new Error("API URL is required");

  let endpoint = `/executions?limit=${limit}`;
  if (workflowId) {
    endpoint += `&workflowId=${workflowId}`;
  }

  const result = await apiFetch(apiUrl, endpoint, apiKey);

  if (!result?.data || !Array.isArray(result.data)) {
    return "No executions found or invalid response format.";
  }

  const executions = result.data.map((e: any) => ({
    id: e.id,
    workflowId: e.workflowId,
    status: e.status,
    startedAt: e.startedAt,
    stoppedAt: e.stoppedAt,
  }));

  return `Found ${executions.length} execution(s):\n${executions
    .map(
      (e: any) =>
        `- Execution ${e.id} (Workflow: ${e.workflowId}, Status: ${e.status}, Started: ${e.startedAt || "N/A"})`
    )
    .join("\n")}`;
}

async function workflowExecute(params: {
  apiUrl: string;
  apiKey?: string;
  workflowId: string;
  payload?: Record<string, any>;
}): Promise<string> {
  const { apiUrl, apiKey, workflowId, payload = {} } = params;
  if (!apiUrl)
    return "❌ **n8n Error: API URL Required**\n\nThe n8n API URL is required to execute workflows. Please provide a valid API URL.";
  if (!workflowId)
    return "❌ **n8n Error: Workflow ID Required**\n\nA workflow ID is required to execute the workflow. Please provide a valid workflow ID.";

  const result = await apiFetch(
    apiUrl,
    `/workflows/${workflowId}/executions`,
    apiKey,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        ...headers(apiKey),
      },
      body: JSON.stringify(payload),
    }
  );

  if (result?.data) {
    return `Workflow execution started successfully:
- Execution ID: ${result.data.id || "N/A"}
- Status: ${result.data.status || "running"}
- Workflow ID: ${result.data.workflowId || workflowId}
- Started: ${result.data.startedAt || new Date().toISOString()}`;
  }

  return `Workflow execution completed: ${JSON.stringify(result, null, 2)}`;
}

// ---------------------------------------------------------------------------
// Generic tools (require user to provide URL + API key)
// ---------------------------------------------------------------------------

export function createN8nGenericTools() {
  const tools: Record<string, any> = {
    n8n_workflow_list: tool({
      description: "List all workflows on an n8n server",
      inputSchema: BaseParams,
      execute: async (params: any) => {
        // Handle defaults
        const cleanParams = {
          apiUrl: params.apiUrl || "",
          apiKey: params.apiKey || "",
        };
        return await workflowList(cleanParams);
      },
    }),

    n8n_workflow_read: tool({
      description: "Read workflow details by ID",
      inputSchema: BaseParams.extend({
        workflowId: z
          .union([z.string(), z.null()])
          .describe("The workflow ID to read. Pass null if not specified."),
      }),
      execute: async (params: any) => {
        // Handle defaults
        const cleanParams = {
          apiUrl: params.apiUrl || "",
          apiKey: params.apiKey || "",
          workflowId: params.workflowId || "",
        };
        return await workflowRead(cleanParams);
      },
    }),

    n8n_workflow_activate: tool({
      description: "Activate a workflow by ID",
      inputSchema: BaseParams.extend({
        workflowId: z
          .union([z.string(), z.null()])
          .describe("The workflow ID to activate. Pass null if not specified."),
      }),
      execute: async (params: any) => {
        // Handle defaults
        const cleanParams = {
          apiUrl: params.apiUrl || "",
          apiKey: params.apiKey || "",
          workflowId: params.workflowId || "",
        };
        return await workflowActivate(cleanParams);
      },
    }),

    n8n_workflow_deactivate: tool({
      description: "Deactivate a workflow by ID",
      inputSchema: BaseParams.extend({
        workflowId: z
          .union([z.string(), z.null()])
          .describe(
            "The workflow ID to deactivate. Pass null if not specified."
          ),
      }),
      execute: async (params: any) => {
        // Handle defaults
        const cleanParams = {
          apiUrl: params.apiUrl || "",
          apiKey: params.apiKey || "",
          workflowId: params.workflowId || "",
        };
        return await workflowDeactivate(cleanParams);
      },
    }),

    n8n_execution_list: tool({
      description: "List recent executions (optionally filter by workflow)",
      inputSchema: BaseParams.extend({
        limit: z
          .union([z.number(), z.null()])
          .describe("Max number of executions. Pass null for default of 20."),
        workflowId: z
          .union([z.string(), z.null()])
          .describe(
            "Optional workflow ID to filter executions. Pass null for all workflows."
          ),
      }),
      execute: async (params: any) => {
        // Handle defaults
        const cleanParams = {
          apiUrl: params.apiUrl || "",
          apiKey: params.apiKey || "",
          limit: params.limit || 20,
          workflowId: params.workflowId || "",
        };
        return await executionList(cleanParams);
      },
    }),

    n8n_workflow_execute: tool({
      description: "Start a workflow execution and return execution info",
      inputSchema: BaseParams.extend({
        workflowId: z
          .union([z.string(), z.null()])
          .describe("The workflow ID to execute. Pass null if not specified."),
        payload: z
          .union([z.record(z.string(), z.any()), z.null()])
          .describe(
            "Optional data to pass to the workflow. Pass null for no input data."
          ),
      }),
      execute: async (params: any) => {
        // Handle defaults
        const cleanParams = {
          apiUrl: params.apiUrl || "",
          apiKey: params.apiKey || "",
          workflowId: params.workflowId || "",
          payload: params.payload || {},
        };
        return await workflowExecute(cleanParams);
      },
    }),
  };

  return tools;
}

// ---------------------------------------------------------------------------
// Server-bound tools (pre-configured URL + API key)
// ---------------------------------------------------------------------------

function safeName(name: string): string {
  return name.toLowerCase().replace(/[^a-z0-9]/g, "_");
}

export function createN8nServerTools(servers: Doc<"n8nServers">[]) {
  const tools: Record<string, any> = {};

  servers
    .filter((s) => s.isEnabled)
    .forEach((server) => {
      const nameSlug = safeName(server.name);
      const baseParams = {
        apiUrl: server.apiUrl,
        apiKey: server.apiKey ?? "",
      };

      const prefix = `n8n_${nameSlug}`;

      // List Workflows – no additional params
      tools[`${prefix}_workflow_list`] = tool({
        description: `[${server.name}] List all workflows`,
        inputSchema: jsonSchema({
          type: "object",
          properties: {},
          required: [],
          additionalProperties: false,
        }),
        execute: () => workflowList(baseParams),
      });

      // Read workflow
      tools[`${prefix}_workflow_read`] = tool({
        description: `[${server.name}] Read workflow details by id`,
        inputSchema: jsonSchema({
          type: "object",
          properties: {
            workflowId: {
              type: "string",
            },
          },
          required: ["workflowId"],
          additionalProperties: false,
        }),
        execute: async (args: any) =>
          workflowRead({ ...baseParams, workflowId: args.workflowId || "" }),
      });

      // Activate workflow
      tools[`${prefix}_workflow_activate`] = tool({
        description: `[${server.name}] Activate workflow by id`,
        inputSchema: jsonSchema({
          type: "object",
          properties: {
            workflowId: {
              type: "string",
            },
          },
          required: ["workflowId"],
          additionalProperties: false,
        }),
        execute: async (args: any) =>
          workflowActivate({
            ...baseParams,
            workflowId: args.workflowId || "",
          }),
      });

      // Deactivate workflow
      tools[`${prefix}_workflow_deactivate`] = tool({
        description: `[${server.name}] Deactivate workflow by id`,
        inputSchema: jsonSchema({
          type: "object",
          properties: {
            workflowId: {
              type: "string",
            },
          },
          required: ["workflowId"],
          additionalProperties: false,
        }),
        execute: async (args: any) =>
          workflowDeactivate({
            ...baseParams,
            workflowId: args.workflowId || "",
          }),
      });

      // List executions
      tools[`${prefix}_execution_list`] = tool({
        description: `[${server.name}] List recent executions (optionally filter by workflow)`,
        inputSchema: jsonSchema({
          type: "object",
          properties: {
            limit: {
              type: "number",
              description: "Max number of executions. Use 20 for default.",
            },
            workflowId: {
              type: "string",
              description:
                "Optional workflow ID to filter executions. Leave empty for all workflows.",
            },
          },
          required: ["limit", "workflowId"],
          additionalProperties: false,
        }),
        execute: async (args: any) => {
          // Handle defaults
          const limit = args.limit || 20;
          const workflowId = args.workflowId || "";
          return executionList({ ...baseParams, limit, workflowId });
        },
      });

      // Execute workflow
      tools[`${prefix}_workflow_execute`] = tool({
        description: `[${server.name}] Execute workflow with optional payload`,
        inputSchema: jsonSchema({
          type: "object",
          properties: {
            workflowId: {
              type: "string",
              description: "The workflow ID to execute.",
            },
            payload: {
              type: "object",
              additionalProperties: false,
              description:
                "Optional data to pass to the workflow. Leave empty for no input data.",
            },
          },
          required: ["workflowId"],
          additionalProperties: false,
        }),
        execute: async (args: any) => {
          // Handle defaults
          const workflowId = args.workflowId || "";
          const payload = args.payload || {};
          return workflowExecute({ ...baseParams, workflowId, payload });
        },
      });
    });

  return tools;
}

// ---------------------------------------------------------------------------
// Metadata for UI
// ---------------------------------------------------------------------------

export function getN8nGenericToolInfo() {
  return [
    {
      id: "n8n_workflow_list",
      name: "n8n: List Workflows",
      description: "List all workflows on any n8n server",
      category: "n8n" as const,
    },
    {
      id: "n8n_workflow_read",
      name: "n8n: Read Workflow",
      description: "Read workflow details by ID",
      category: "n8n" as const,
    },
    {
      id: "n8n_workflow_activate",
      name: "n8n: Activate Workflow",
      description: "Activate a workflow by ID",
      category: "n8n" as const,
    },
    {
      id: "n8n_workflow_deactivate",
      name: "n8n: Deactivate Workflow",
      description: "Deactivate a workflow by ID",
      category: "n8n" as const,
    },
    {
      id: "n8n_execution_list",
      name: "n8n: List Executions",
      description: "List recent workflow executions",
      category: "n8n" as const,
    },
    {
      id: "n8n_workflow_execute",
      name: "n8n: Execute Workflow",
      description: "Execute a workflow with optional data",
      category: "n8n" as const,
    },
  ];
}

// ---------------------------------------------------------------------------
// Metadata for UI – one entry per server
// ---------------------------------------------------------------------------

export function getN8nServerToolInfo(servers: Doc<"n8nServers">[]) {
  return servers
    .filter((s) => s.isEnabled)
    .map((server) => ({
      id: `n8n_${safeName(server.name)}`,
      name: `n8n: ${server.name}`,
      description: server.description || `Tools for ${server.name} n8n server`,
      category: "n8n" as const,
      apiUrl: server.apiUrl,
      availableTools: server.availableTools ?? [],
    }));
}
