import { memo } from 'react';
import { Skeleton } from '@/components/ui/skeleton';

interface AttachmentData {
  name?: string;
  size?: number;
  type?: string;
  mimeType?: string;
  contentType?: string;
  url?: string;
  storageId?: string;
  extractedText?: string;
}

interface AttachmentPlaceholderProps {
  attachment: AttachmentData;
  onClick?: () => void;
  isLoading?: boolean;
  className?: string;
}

// Helper function to get file icon based on type
const getFileIcon = (name: string, type: string) => {
  if (type.startsWith('image/')) return '🖼️';
  if (type.startsWith('audio/')) return '🎵';
  if (type.startsWith('video/')) return '🎬';
  if (name.toLowerCase().endsWith('.pdf') || type === 'application/pdf') return '📄';
  if (name.toLowerCase().endsWith('.txt') || type === 'text/plain') return '📝';
  if (name.toLowerCase().endsWith('.csv') || type === 'text/csv') return '📊';
  if (name.toLowerCase().endsWith('.json') || type === 'application/json') return '📋';
  if (name.toLowerCase().endsWith('.md')) return '📖';
  if (name.toLowerCase().endsWith('.docx') || type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') return '📃';
  if (name.toLowerCase().endsWith('.doc') || type === 'application/msword') return '📑';
  if (name.toLowerCase().endsWith('.rtf') || type === 'application/rtf' || type === 'text/rtf') return '📜';
  return '📎';
};

const formatFileSize = (bytes?: number) => {
  if (!bytes) return '';
  const sizes = ['B', 'KB', 'MB'];
  let size = bytes;
  let i = 0;
  while (size >= 1024 && i < 2) {
    size /= 1024;
    i++;
  }
  return ` (${size.toFixed(1)} ${sizes[i]})`;
};

const getFileTypeDisplay = (name: string, type: string) => {
  if (type.startsWith('image/')) return 'Image';
  if (type.startsWith('audio/')) return 'Audio';
  if (type.startsWith('video/')) return 'Video';
  if (name.toLowerCase().endsWith('.pdf') || type === 'application/pdf') return 'PDF Document';
  if (name.toLowerCase().endsWith('.docx') || type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') return 'Word Document';
  if (name.toLowerCase().endsWith('.doc') || type === 'application/msword') return 'Word Document';
  if (name.toLowerCase().endsWith('.rtf') || type === 'application/rtf' || type === 'text/rtf') return 'Rich Text Document';
  if (name.toLowerCase().endsWith('.txt') || type === 'text/plain') return 'Text Document';
  if (name.toLowerCase().endsWith('.csv') || type === 'text/csv') return 'CSV Document';
  if (name.toLowerCase().endsWith('.json') || type === 'application/json') return 'JSON Document';
  if (name.toLowerCase().endsWith('.md')) return 'Markdown Document';
  return 'File';
};

export const AttachmentPlaceholder = memo<AttachmentPlaceholderProps>(function AttachmentPlaceholder({
  attachment,
  onClick,
  isLoading = false,
  className = ''
}) {
  const fileType = attachment.mimeType || attachment.contentType || attachment.type || '';
  const fileName = attachment.name || 'File';
  const icon = getFileIcon(fileName, fileType);
  const fileTypeDisplay = getFileTypeDisplay(fileName, fileType);

  const handleClick = () => {
    if (attachment.url) {
      window.open(attachment.url, '_blank');
    }
    onClick?.();
  };

  if (isLoading) {
    return (
      <div className={`flex items-center gap-3 p-4 bg-muted/30 rounded-lg border border-border/30 ${className}`}>
        <Skeleton className="w-8 h-8 rounded" />
        <div className="flex-1 min-w-0 space-y-2">
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="h-3 w-1/2" />
        </div>
      </div>
    );
  }

  return (
    <div 
      className={`flex items-center gap-3 p-4 bg-muted/30 rounded-lg border border-border/30 hover:bg-muted/40 transition-colors cursor-pointer ${className}`}
      onClick={handleClick}
      role="button"
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          handleClick();
        }
      }}
    >
      <span className="text-2xl flex-shrink-0">{icon}</span>
      <div className="flex-1 min-w-0">
        <div className="text-sm font-medium truncate">{fileName}</div>
        <div className="text-xs text-muted-foreground">
          {fileTypeDisplay}{formatFileSize(attachment.size)}
        </div>
        {attachment.extractedText && (
          <div className="text-xs text-muted-foreground mt-1 line-clamp-2">
            Text extracted for AI analysis
          </div>
        )}
      </div>
      <div className="text-xs text-muted-foreground flex-shrink-0">
        Click to view
      </div>
    </div>
  );
});