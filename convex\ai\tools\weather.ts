"use node";

import { tool } from "ai";
import { getWeatherData } from "../utils/weather";
import { jsonSchema } from "ai";

export function createWeatherTool(ctx: any) {
  return tool({
    description:
      "Get current weather conditions or forecast for specific locations. **Important limitations**: Forecast data is only available for the next 5 days from today. For dates beyond 5 days, I can only provide current conditions. Use when users ask for weather information.",
    inputSchema: jsonSchema({
      type: "object",
      properties: {
        location: {
          type: "string",
          description:
            "The city, region, or location for the weather report (e.g., 'New York City', 'London, UK', 'Tokyo, Japan')",
        },
        date: {
          type: "string",
          description:
            "The desired date for forecast in YYYY-MM-DD format, or relative terms like 'today', 'tomorrow', 'in 3 days'. Leave empty for current weather. Note: Forecasts only available for next 5 days.",
        },
        forecast_type: {
          type: "string",
          enum: ["current", "daily", "detailed", "extended"],
          description:
            "Type of weather information: current (now), daily (day overview), detailed (hourly), extended (multi-day). Use 'current' for default.",
        },
      },
      required: ["location", "date", "forecast_type"],
      additionalProperties: false,
    }),
    execute: async (args: any): Promise<string> => {
      const { location, date = "", forecast_type = "current" } = args;

      // Validate location
      if (!location || location.trim() === "") {
        return `Error: No location provided for weather lookup.

Please specify a location such as:
- "New York City"
- "London, UK" 
- "Tokyo, Japan"
- "San Francisco, CA"
- "Paris, France"`;
      }

      // Parse and validate date if provided
      let processedDate = "";
      let forecastDays = 0;

      if (date && date.trim() !== "") {
        const dateValidation = validateWeatherDate(date.trim());

        if (dateValidation.error) {
          return `Error: ${dateValidation.error}

**Weather Forecast Limitations:**
- ✅ Current weather: Available anytime
- ✅ Forecast: Available for next 5 days only
- ❌ Historical data: Not available
- ❌ Long-term forecasts: Beyond 5 days not supported

Please use:
- "today" or null for current weather
- "tomorrow", "in 2 days", etc. for forecasts
- Specific dates in YYYY-MM-DD format (within next 5 days)`;
        }

        if (dateValidation.tooFar) {
          return `⚠️ **Forecast Limitation Exceeded**

You requested weather for: ${dateValidation.requestedDate}
This is ${dateValidation.daysFromNow} days from now.

**Weather forecasts are only available for the next 5 days.**

Available options:
- Current weather for ${location}
- Forecast for ${location} (next 1-5 days)
- Try a date within the next 5 days

Would you like current weather conditions instead?`;
        }

        processedDate = dateValidation.processedDate;
        forecastDays = dateValidation.daysFromNow;
      }

      // Determine the type of weather request
      const requestType = determineWeatherRequestType(
        date,
        forecast_type,
        forecastDays
      );

      try {
        const result = await getWeatherData(
          ctx,
          location.trim(),
          processedDate
        );

        // Add contextual information based on request type
        let contextualInfo = "";

        if (forecastDays > 0) {
          contextualInfo = `\n\n📊 **Forecast Information:**
- This is a ${forecastDays}-day forecast
- Forecast accuracy decreases beyond 3 days
- For more detailed hourly forecasts, specify forecast_type as "detailed"`;
        } else if (!date) {
          contextualInfo = `\n\n🕐 **Current Conditions:**
- Data updated in real-time
- For forecast information, specify a future date (next 1-5 days)`;
        }

        return result + contextualInfo;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Unknown error";

        // Provide helpful error context
        let helpfulContext = "";
        if (
          errorMessage.includes("location") ||
          errorMessage.includes("not found")
        ) {
          helpfulContext = `\n\n💡 **Location Tips:**
- Try adding country/state: "${location}, US" or "${location}, UK"
- Use major city names when possible
- Check spelling of the location name`;
        }

        return `Unable to get weather information for ${location}. Error: ${errorMessage}${helpfulContext}`;
      }
    },
  });
}

// Helper function to validate weather dates and check forecast limitations
function validateWeatherDate(dateInput: string): {
  error?: string;
  tooFar?: boolean;
  processedDate: string;
  daysFromNow: number;
  requestedDate: string;
} {
  const today = new Date();
  today.setHours(0, 0, 0, 0); // Reset time for accurate day comparison

  let targetDate: Date;
  let interpretation = "";

  // Handle relative dates
  const lowerInput = dateInput.toLowerCase().trim();

  if (lowerInput === "today" || lowerInput === "now") {
    targetDate = new Date(today);
    interpretation = "today";
  } else if (lowerInput === "tomorrow") {
    targetDate = new Date(today.getTime() + 24 * 60 * 60 * 1000);
    interpretation = "tomorrow";
  } else if (lowerInput === "yesterday") {
    return {
      error:
        "Historical weather data is not available. Please request current weather or forecasts for future dates (next 1-5 days).",
      processedDate: "",
      daysFromNow: -1,
      requestedDate: dateInput,
    };
  } else {
    // Handle "in X days" format
    const relativeMatch = lowerInput.match(/^in\s+(\d+)\s+days?$/);
    if (relativeMatch) {
      const daysAhead = parseInt(relativeMatch[1]);
      targetDate = new Date(today.getTime() + daysAhead * 24 * 60 * 60 * 1000);
      interpretation = `in ${daysAhead} day${daysAhead !== 1 ? "s" : ""}`;
    } else {
      // Try parsing as a specific date
      const parsedDate = new Date(dateInput);
      if (isNaN(parsedDate.getTime())) {
        return {
          error: `Unable to parse "${dateInput}" as a valid date. Use formats like "tomorrow", "in 3 days", or "2025-01-15".`,
          processedDate: "",
          daysFromNow: 0,
          requestedDate: dateInput,
        };
      }
      targetDate = parsedDate;
      interpretation = dateInput;
    }
  }

  // Calculate days from now
  const daysFromNow = Math.ceil(
    (targetDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)
  );

  // Check if date is in the past
  if (daysFromNow < 0) {
    return {
      error:
        "Historical weather data is not available. Please request current weather or forecasts for future dates (next 1-5 days).",
      processedDate: "",
      daysFromNow,
      requestedDate: interpretation,
    };
  }

  // Check if date is too far in the future (beyond 5 days)
  if (daysFromNow > 5) {
    return {
      error: "",
      tooFar: true,
      processedDate: "",
      daysFromNow,
      requestedDate: interpretation,
    };
  }

  // Format date for API
  const processedDate = targetDate.toISOString().split("T")[0];

  return {
    processedDate,
    daysFromNow,
    requestedDate: interpretation,
  };
}

// Helper function to determine the type of weather request
function determineWeatherRequestType(
  date: string | null,
  forecastType: string | null,
  daysFromNow: number
): string {
  if (!date || daysFromNow === 0) {
    return "current";
  }

  if (forecastType) {
    return forecastType;
  }

  // Auto-determine based on time frame
  if (daysFromNow === 1) return "daily";
  if (daysFromNow <= 3) return "detailed";
  return "extended";
}
